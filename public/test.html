<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 测试自定义样式 */
        .test-apple-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-apple-button {
            background: #007AFF;
            color: white;
            border: none;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .test-apple-button:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">样式测试页面</h1>
        
        <!-- 测试基础 Tailwind 样式 -->
        <div class="bg-white rounded-lg p-6 mb-6 shadow-lg">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">基础 Tailwind 样式测试</h2>
            <p class="text-gray-600 mb-4">这是一个测试基础 Tailwind CSS 样式的示例。</p>
            <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                基础按钮
            </button>
        </div>

        <!-- 测试自定义 Apple 样式 */}
        <div class="test-apple-card rounded-2xl p-6 mb-6">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">自定义 Apple 样式测试</h2>
            <p class="text-gray-600 mb-4">这是一个测试自定义 Apple 风格样式的示例。</p>
            <button class="test-apple-button px-6 py-2 rounded-xl">
                Apple 按钮
            </button>
        </div>

        <!-- 测试颜色 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-500 text-white p-4 rounded-lg text-center">Blue</div>
            <div class="bg-green-500 text-white p-4 rounded-lg text-center">Green</div>
            <div class="bg-orange-500 text-white p-4 rounded-lg text-center">Orange</div>
            <div class="bg-red-500 text-white p-4 rounded-lg text-center">Red</div>
        </div>

        <!-- 测试响应式 -->
        <div class="bg-white rounded-lg p-6 shadow-lg">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">响应式测试</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-gray-200 p-4 rounded">项目 1</div>
                <div class="bg-gray-200 p-4 rounded">项目 2</div>
                <div class="bg-gray-200 p-4 rounded">项目 3</div>
            </div>
        </div>
    </div>
</body>
</html>
