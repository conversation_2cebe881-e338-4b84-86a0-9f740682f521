# 茶馆收银系统 - 菜单设计规划

## 设计风格定位

### Apple 风格特点
- **极简主义**：大量留白，简洁布局
- **优雅字体**：SF Pro 字体系列，清晰的层级
- **精致色彩**：中性色调为主，蓝色作为主要交互色
- **流畅动画**：微妙的过渡效果和状态变化
- **毛玻璃效果**：backdrop-filter 实现的现代感
- **圆角设计**：统一的圆角半径，柔和的视觉效果

## 主要功能模块

### 1. 🏠 主页面 (Dashboard)
**功能概述**：系统概览和快速操作入口

**主要内容**：
- 今日营业数据概览
- 快速功能入口
- 最近订单列表
- 系统通知和提醒

**UI 元素**：
- 数据卡片（销售额、订单数、客流量）
- 功能网格（收银、库存、会员、报表）
- 时间线组件（最近活动）
- 状态指示器

### 2. 💰 收银台 (POS)
**功能概述**：核心收银功能

**主要功能**：
- 商品选择和搜索
- 购物车管理
- 支付方式选择
- 小票打印
- 会员积分处理

**UI 布局**：
- 左侧：商品分类和列表
- 中间：商品详情和操作
- 右侧：购物车和结算

### 3. 📦 库存管理 (Inventory)
**功能概述**：商品和库存管理

**子模块**：
- 商品管理（增删改查）
- 分类管理
- 库存盘点
- 进货管理
- 库存预警

**UI 特色**：
- 表格视图和卡片视图切换
- 批量操作功能
- 搜索和筛选
- 库存状态可视化

### 4. 👥 会员管理 (Members)
**功能概述**：客户关系管理

**主要功能**：
- 会员信息管理
- 积分系统
- 会员等级
- 消费记录
- 营销活动

**UI 组件**：
- 会员卡片设计
- 积分进度条
- 消费统计图表
- 活动横幅

### 5. 📊 数据报表 (Reports)
**功能概述**：数据分析和报表

**报表类型**：
- 销售报表
- 库存报表
- 会员分析
- 财务报表
- 经营分析

**可视化元素**：
- 图表组件（柱状图、折线图、饼图）
- 数据表格
- 时间选择器
- 导出功能

### 6. ⚙️ 系统设置 (Settings)
**功能概述**：系统配置和管理

**设置分类**：
- 基本设置（店铺信息、营业时间）
- 用户管理（员工账号、权限）
- 支付设置（支付方式、费率）
- 打印设置（小票模板、打印机）
- 系统维护（数据备份、日志）

## 导航设计

### 主导航 (Primary Navigation)
采用 Apple 风格的标签栏设计：

```
┌─────────────────────────────────────────────────────────┐
│  🏠 主页    💰 收银    📦 库存    👥 会员    📊 报表  │
└─────────────────────────────────────────────────────────┘
```

### 二级导航 (Secondary Navigation)
在需要的页面使用侧边栏或顶部标签：

**库存管理示例**：
- 商品管理
- 分类管理  
- 库存盘点
- 进货管理
- 库存预警

### 移动端导航
底部标签栏 + 汉堡菜单：

```
┌─────────────────────────────────────────┐
│                内容区域                  │
│                                        │
└─────────────────────────────────────────┘
│ 🏠  💰  📦  👥  ⋯ │
└─────────────────────┘
```

## 页面布局规范

### 桌面端布局
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                            │
├─────────────────────────────────────────────────────────┤
│                                                        │
│                    主要内容区域                         │
│                                                        │
└─────────────────────────────────────────────────────────┘
```

### 移动端布局
```
┌─────────────────────────────────┐
│            顶部标题栏            │
├─────────────────────────────────┤
│                                │
│                                │
│            内容区域             │
│                                │
│                                │
├─────────────────────────────────┤
│            底部导航             │
└─────────────────────────────────┘
```

## 色彩规范

### 主色调
- **主色**：#007AFF (Apple Blue)
- **成功色**：#34C759 (Apple Green)
- **警告色**：#FF9500 (Apple Orange)
- **错误色**：#FF3B30 (Apple Red)

### 中性色
- **文字主色**：#1D1D1F
- **文字次色**：#86868B
- **背景色**：#F5F5F7
- **卡片背景**：#FFFFFF
- **边框色**：#D1D1D6

### 茶馆主题色
- **茶绿色**：#34C759 (与成功色统一)
- **深茶色**：#2D5016
- **浅茶色**：#E8F5E8

## 字体规范

### 字体族
- **标题字体**：SF Pro Display
- **正文字体**：SF Pro Text
- **等宽字体**：SF Mono (用于数字和代码)

### 字体大小
- **大标题**：28px (1.75rem)
- **标题**：24px (1.5rem)
- **副标题**：20px (1.25rem)
- **正文**：16px (1rem)
- **小字**：14px (0.875rem)
- **说明文字**：12px (0.75rem)

### 字重
- **细体**：300
- **常规**：400
- **中等**：500
- **半粗**：600
- **粗体**：700

## 组件设计

### 按钮组件
- **主要按钮**：蓝色背景，白色文字
- **次要按钮**：白色背景，蓝色边框
- **危险按钮**：红色背景，白色文字
- **文字按钮**：无背景，蓝色文字

### 输入框组件
- **标准输入框**：圆角，浅灰边框
- **聚焦状态**：蓝色边框，蓝色阴影
- **错误状态**：红色边框，红色阴影
- **禁用状态**：灰色背景，灰色文字

### 卡片组件
- **标准卡片**：白色背景，轻微阴影
- **毛玻璃卡片**：半透明背景，模糊效果
- **数据卡片**：突出数字，简洁布局

### 表格组件
- **斑马纹**：交替行背景色
- **悬停效果**：行高亮
- **排序功能**：列标题可点击
- **分页控件**：Apple 风格的页码

## 动画效果

### 页面转场
- **淡入淡出**：opacity 变化
- **滑动**：transform translateX/Y
- **缩放**：transform scale
- **弹性**：cubic-bezier 缓动

### 交互反馈
- **按钮点击**：轻微缩放
- **悬停效果**：颜色渐变
- **加载状态**：旋转动画
- **状态变化**：平滑过渡

## 响应式设计

### 断点设置
- **手机**：< 768px
- **平板**：768px - 1024px
- **桌面**：> 1024px

### 适配策略
- **移动优先**：从小屏幕开始设计
- **渐进增强**：大屏幕添加更多功能
- **触摸友好**：按钮大小至少 44px
- **内容优先**：重要信息优先显示

## 可访问性

### 颜色对比
- **文字对比度**：至少 4.5:1
- **大文字对比度**：至少 3:1
- **非文字对比度**：至少 3:1

### 键盘导航
- **Tab 顺序**：逻辑清晰
- **焦点指示**：明显的焦点样式
- **快捷键**：常用功能支持快捷键

### 屏幕阅读器
- **语义化标签**：正确使用 HTML 标签
- **ARIA 标签**：补充语义信息
- **替代文本**：图片和图标的描述

这个设计规范将确保整个茶馆收银系统具有统一的 Apple 风格界面，提供优秀的用户体验。
