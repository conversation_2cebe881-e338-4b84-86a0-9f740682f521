# ProductModal 图片上传功能

## 功能概述

ProductModal 组件已经更新，支持图片文件上传功能，替代了原来的图片URL输入框。

## 主要变更

### 1. API 更新

在 `src/services/api.js` 中新增了文件上传API：

```javascript
// 文件上传相关 API
export const fileAPI = {
    // 上传文件
    uploadFile: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        
        return api.post('/api/v1/file/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },
};
```

### 2. 组件功能更新

#### 新增状态管理
- `selectedFile`: 存储用户选择的图片文件
- `imagePreview`: 存储图片预览URL

#### 文件处理功能
- **文件类型验证**: 支持 JPG、PNG、GIF 格式
- **文件大小限制**: 最大 5MB
- **图片预览**: 选择文件后立即显示预览
- **删除功能**: 可以移除已选择的图片

#### 上传流程
1. 用户选择图片文件
2. 验证文件类型和大小
3. 显示图片预览
4. 点击保存时，如果有新图片则先调用 `/api/v1/file/upload` 上传
5. 获取返回的 `fileUrl` 并设置到商品的 `imageUrl` 字段
6. 提交商品数据

## API 接口规范

### 文件上传接口

**请求**
- URL: `/api/v1/file/upload`
- Method: `POST`
- Content-Type: `multipart/form-data`
- 参数: `@RequestParam("file") MultipartFile file`

**响应**
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "originalName": "image.jpg",
        "size": 67716,
        "success": true,
        "fileUrl": "http://t1l8c4oaa.hn-bkt.clouddn.com/pic1",
        "message": "文件上传成功"
    },
    "timestamp": 1756191953076
}
```

## 使用方式

### 新增商品
1. 打开新增商品弹窗
2. 填写商品基本信息
3. 点击图片上传区域选择图片文件
4. 预览图片确认无误
5. 点击"创建"按钮保存

### 编辑商品
1. 打开编辑商品弹窗（会显示现有图片）
2. 如需更换图片，点击上传区域选择新图片
3. 如需删除图片，点击预览图片右上角的删除按钮
4. 点击"保存"按钮更新

### 查看商品
- 只读模式下会显示商品的现有图片（如果有）

## 错误处理

- **文件类型错误**: 提示"请选择有效的图片文件（JPG、PNG、GIF）"
- **文件过大**: 提示"图片文件大小不能超过5MB"
- **上传失败**: 提示"图片上传失败，请重试"
- **网络错误**: 提示"操作失败，请重试"

## 技术细节

### 文件验证
```javascript
const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const maxSize = 5 * 1024 * 1024; // 5MB
```

### 图片预览
使用 `FileReader` API 生成本地预览：
```javascript
const reader = new FileReader();
reader.onload = (e) => {
    setImagePreview(e.target.result);
};
reader.readAsDataURL(file);
```

### 上传时序
1. 验证表单数据
2. 如果有新图片文件，先上传图片
3. 获取图片URL后，更新商品数据
4. 提交商品创建/更新请求

## 测试

运行测试文件验证功能：
```bash
npm test src/test/ProductModal.test.js
```

测试覆盖：
- 组件渲染
- 文件选择和验证
- 图片预览
- 上传流程
- 错误处理
