# 这是一个标准的、适用于大多数前端项目的 .gitignore 文件。
# 它能有效地忽略掉依赖、构建产物、日志、系统文件和编辑器配置等。

# 依赖文件夹
# 这是最重要的一项，node_modules 体积巨大且可以通过 npm install 重新生成。
node_modules/

# 构建产物文件夹
# create-react-app, a-vite-project 等工具的默认构建输出目录。
build/
dist/
.output/
.next/

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 环境变量文件
# 通常包含敏感信息（如API密钥），应该通过CI/CD或其他方式注入。
# .env.local 是一个常见的约定，用于本地开发覆盖。
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# 临时目录和缓存文件
.tmp
.cache
.vite/
.eslintcache

# 编辑器和IDE配置文件
# 避免将个人编辑器偏好提交到团队项目中。
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp

# 系统文件
.DS_Store
Thumbs.db