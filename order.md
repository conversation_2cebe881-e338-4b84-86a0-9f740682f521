1、创建订单
POST /api/v1/orders/create
request:
{"orderType":"DINE_IN","items":[{"productId":2,"quantity":2},{"productId":3,"quantity":5}],"memberId":204849871042252800,"discounts":[{"discountType":"MEMBER","description":"会员优惠","amount":50,"referenceId":75420041}],"remark":"第一份订单"}
response:
{"code":200,"msg":"订单创建成功","timestamp":1753280470709}
2、分页查询订单
POST /api/v1/orders/query
request:
{
"memberId": 77884999,
"size": 10,
"current": 1,
"endTime": "1994-01-12 02:46:46",
"paymentStatus": "UNPAID",
"orderType": "DINE_IN",
"startTime": "1997-03-29 22:15:56",
"tableId": 1,
"status": "PENDING_PAYMENT",
"orderNo": "123"
}
response:
{"code":200,"msg":"操作成功","data":{"current":1,"size":10,"total":1,"pages":1,"records":[{"id":2,"orderNo":"20250723222110445333","storeId":1,"memberId":204849871042252800,"tableId":null,"orderType":"DINE_IN","orderTypeDescription":"堂食","status":"PENDING_PAYMENT","statusDescription":"待支付","paymentStatus":"UNPAID","paymentStatusDescription":"未支付","originalAmount":740.00,"discountAmount":50.00,"payableAmount":690.00,"remark":"第一份订单","createdAt":"2025-07-23T22:21:10.638517","updatedAt":"2025-07-23T22:21:10.64007","items":null,"discounts":null}]},"timestamp":1753280723204}
3、根据ID查询订单详情
GET /api/v1/orders/{id}
response:
{"code":200,"msg":"操作成功","data":{"id":2,"orderNo":"20250723222110445333","storeId":1,"memberId":204849871042252800,"tableId":null,"orderType":"DINE_IN","orderTypeDescription":"堂食","status":"PENDING_PAYMENT","statusDescription":"待支付","paymentStatus":"UNPAID","paymentStatusDescription":"未支付","originalAmount":740.00,"discountAmount":50.00,"payableAmount":690.00,"remark":"第一份订单","createdAt":"2025-07-23T22:21:10.638517","updatedAt":"2025-07-23T22:21:10.64007","items":[{"id":1,"orderId":2,"productId":2,"productName":"正山小种222","quantity":2,"unitPrice":120.00,"totalPrice":240.00},{"id":2,"orderId":2,"productId":3,"productName":"秀牙","quantity":5,"unitPrice":100.00,"totalPrice":500.00}],"discounts":[{"id":1,"orderId":2,"discountType":"MEMBER","discountTypeDescription":"会员折扣","description":"会员优惠","amount":50.00,"referenceId":75420041}]},"timestamp":1753280852860}
4、更新订单状态
POST /api/v1/orders/update-status
request:
{
"orderId": 2,
"status": "PENDING_PAYMENT"
}
response:
{
"code": 200,
"msg": "订单状态更新成功",
"data": null,
"timestamp": 1753280947782
}
5、更新订单支付状态
POST /api/v1/orders/update-payment-status
request:
{
"orderId": 2,
"paymentStatus": "REFUNDED"
}
response:
{
"code": 200,
"msg": "订单支付状态更新成功",
"data": null,
"timestamp": 1753280980000
}
6、删除订单
DELETE /api/v1/orders/{id}
response: