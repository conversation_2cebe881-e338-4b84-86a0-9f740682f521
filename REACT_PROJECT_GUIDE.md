# 茶馆收银系统 - React 项目指南

## 项目概述

这是一个基于 React 18 的现代化茶馆收银管理系统，采用 Apple 设计风格，支持完整的 POS 功能。

## 技术栈

- **前端框架**: React 18.2+
- **路由管理**: React Router DOM 6+
- **HTTP 客户端**: Axios
- **样式框架**: Tailwind CSS 3+
- **图标库**: FontAwesome 6+
- **状态管理**: React Context + useReducer
- **构建工具**: Create React App

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── Layout.js       # 布局组件
│   ├── Sidebar.js      # 侧边栏组件
│   └── ProtectedRoute.js # 路由保护组件
├── pages/              # 页面组件
│   ├── Login.js        # 登录页面
│   ├── Dashboard.js    # 主页面
│   └── POS.js          # 收银台页面
├── contexts/           # React Context
│   └── AuthContext.js  # 认证上下文
├── services/           # API 服务
│   └── api.js          # API 接口定义
├── hooks/              # 自定义 Hooks
│   └── useApi.js       # API 请求 Hooks
├── utils/              # 工具函数
│   └── config.js       # 配置文件
├── assets/             # 静态资源
├── App.js              # 主应用组件
├── index.js            # 应用入口
└── index.css           # 全局样式
```

## 开发环境配置

### 1. 安装依赖

```bash
npm install
```

### 2. 环境变量

创建 `.env` 文件：

```env
REACT_APP_API_BASE_URL=http://127.0.0.1:8081
REACT_APP_ENV=development
```

### 3. 启动开发服务器

```bash
npm start
```

应用将在 `http://localhost:3000` 启动。

### 4. API 服务器

确保后端 API 服务器运行在 `http://127.0.0.1:8081`。

## 核心功能

### 🔐 认证系统

- **登录/登出**: 基于 JWT Token
- **路由保护**: 未登录自动跳转
- **状态管理**: React Context 全局状态
- **自动刷新**: Token 过期自动处理

### 🎨 UI 设计

- **Apple 风格**: 遵循 Apple 设计规范
- **响应式布局**: 适配桌面端和移动端
- **毛玻璃效果**: backdrop-filter 实现
- **流畅动画**: CSS 过渡和动画

### 📱 页面结构

1. **登录页面** (`/login`)
   - 用户名/密码登录
   - 记住登录状态
   - 错误处理和反馈

2. **主页面** (`/dashboard`)
   - 数据概览卡片
   - 快速功能入口
   - 最近订单和通知

3. **收银台** (`/pos`)
   - 商品选择和分类
   - 购物车管理
   - 多种支付方式

## API 接口

### 基础配置

```javascript
// src/utils/config.js
export const API_CONFIG = {
    development: {
        baseURL: 'http://127.0.0.1:8081',
        timeout: 10000,
    }
};
```

### 认证接口

```javascript
// 登录
POST /api/v1/auth/login
{
    "username": "admin",
    "password": "123456"
}

// 响应
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "accessToken": "...",
        "refreshToken": "...",
        "staffId": 1,
        "username": "admin",
        "fullName": "管理员"
    }
}
```

### 使用示例

```javascript
import { authAPI } from '../services/api';

// 登录
const result = await authAPI.login({ username, password });

// 获取商品列表
const products = await productAPI.getProducts();
```

## 状态管理

### AuthContext 使用

```javascript
import { useAuth } from '../contexts/AuthContext';

function MyComponent() {
    const { user, login, logout, isAuthenticated } = useAuth();
    
    const handleLogin = async () => {
        const result = await login(credentials);
        if (result.success) {
            // 登录成功
        }
    };
    
    return (
        <div>
            {isAuthenticated ? (
                <p>欢迎，{user.fullName}</p>
            ) : (
                <button onClick={handleLogin}>登录</button>
            )}
        </div>
    );
}
```

## 自定义 Hooks

### useApi Hook

```javascript
import { useApi } from '../hooks/useApi';
import { productAPI } from '../services/api';

function ProductList() {
    const { data, loading, error, refetch } = useApi(
        () => productAPI.getProducts()
    );
    
    if (loading) return <div>加载中...</div>;
    if (error) return <div>错误: {error}</div>;
    
    return (
        <div>
            {data.map(product => (
                <div key={product.id}>{product.name}</div>
            ))}
        </div>
    );
}
```

## 样式系统

### Tailwind CSS 配置

```javascript
// tailwind.config.js
module.exports = {
    content: ["./src/**/*.{js,jsx,ts,tsx}"],
    theme: {
        extend: {
            colors: {
                'apple-blue': '#007AFF',
                'apple-green': '#34C759',
            }
        }
    }
};
```

### Apple 风格组件

```javascript
// 使用预定义的 Apple 风格类
<button className="apple-button rounded-apple">
    点击按钮
</button>

<div className="apple-card rounded-2xl p-6">
    卡片内容
</div>
```

## 部署

### 构建生产版本

```bash
npm run build
```

### 环境变量

生产环境需要设置：

```env
REACT_APP_API_BASE_URL=https://your-api-domain.com
REACT_APP_ENV=production
```

## 开发规范

### 1. 组件命名

- 使用 PascalCase: `MyComponent.js`
- 文件名与组件名一致
- 目录使用 camelCase: `components/`

### 2. 代码风格

- 使用 ES6+ 语法
- 优先使用函数组件和 Hooks
- 保持组件单一职责

### 3. 状态管理

- 本地状态使用 `useState`
- 全局状态使用 Context
- 复杂状态使用 `useReducer`

### 4. API 调用

- 统一使用 `services/api.js`
- 错误处理要完善
- 使用自定义 Hooks 封装

## 常见问题

### Q: 如何添加新的页面？

1. 在 `src/pages/` 创建组件
2. 在 `src/App.js` 添加路由
3. 在 `src/utils/config.js` 添加路由常量

### Q: 如何添加新的 API 接口？

1. 在 `src/services/api.js` 添加接口定义
2. 使用 `useApi` Hook 调用
3. 处理错误和加载状态

### Q: 如何自定义样式？

1. 优先使用 Tailwind CSS 类
2. 自定义样式添加到 `src/index.css`
3. 遵循 Apple 设计规范

## 更新日志

### v1.0.0 (2024-01-20)

- ✅ 完成基础项目结构
- ✅ 实现认证系统
- ✅ 完成登录和主页面
- ✅ 实现收银台功能
- ✅ 添加 Apple 风格 UI
- ✅ 配置开发环境

---

© 2024 茶馆收银系统. 保留所有权利.
