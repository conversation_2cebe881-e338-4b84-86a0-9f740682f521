import axios from 'axios';
import { getCurrentConfig, STORAGE_KEYS } from '../utils/config';

// 创建 axios 实例
const api = axios.create({
    baseURL: getCurrentConfig().baseURL,
    timeout: getCurrentConfig().timeout,
    headers: {
        'Content-Type': 'application/json',
    },
});

// 请求拦截器
api.interceptors.request.use(
    (config) => {
        // 添加认证 token
        const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        
        console.log('🚀 API Request:', config.method?.toUpperCase(), config.url);
        return config;
    },
    (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    (response) => {
        console.log('✅ API Response:', response.status, response.config.url);
        return response;
    },
    (error) => {
        console.error('❌ Response Error:', error.response?.status, error.config?.url);
        
        // 处理 401 未授权错误
        if (error.response?.status === 401) {
            // 清除本地存储的认证信息
            localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.USER_INFO);
            
            // 跳转到登录页面
            window.location.href = '/login';
        }
        
        return Promise.reject(error);
    }
);

// 认证相关 API
export const authAPI = {
    // 登录
    login: (credentials) => api.post('/api/v1/auth/login', credentials),
    
    // 登出
    logout: () => api.post('/api/v1/auth/logout'),
    
    // 刷新 token
    refreshToken: (refreshToken) => api.post('/api/v1/auth/refresh', { refreshToken }),
    
    // 获取用户信息
    getUserInfo: () => api.get('/api/v1/auth/user'),
};

// 商品相关 API
export const productAPI = {
    // 获取商品列表
    getProducts: (params) => api.get('/api/v1/products', { params }),

    // 分页查询商品
    queryProducts: (data) => api.post('/api/v1/products/query', data),

    // 获取商品详情
    getProduct: (id) => api.get(`/api/v1/products/${id}`),

    // 创建商品
    createProduct: (data) => api.post('/api/v1/products/create', data),

    // 更新商品
    updateProduct: (data) => api.post('/api/v1/products/update', data),

    // 删除商品
    deleteProduct: (id) => api.post(`/api/v1/products/delete/${id}`),

    // 根据分类查询商品
    getProductsByCategory: (categoryId) => api.get(`/api/v1/products/category/${categoryId}`),

    // 获取顶级分类
    getTopLevelCategories: () => api.get('/api/v1/product-categories/top-level'),

    // 上架商品
    publishProduct: (id) => api.post(`/api/v1/products/publish/${id}`),

    // 下架商品
    archiveProduct: (id) => api.post(`/api/v1/products/archive/${id}`),

    // 标记为售罄/取消售罄 (同一个接口)
    toggleSoldOut: (id) => api.post(`/api/v1/products/sold-out/${id}`),

    // 获取商品分类
    getCategories: () => api.get('/api/v1/products/categories'),
};

// 商品分类相关 API
export const categoryAPI = {
    // 获取顶级分类
    getTopLevelCategories: () => api.get('/api/v1/product-categories/top-level'),

    // 创建分类
    createCategory: (data) => api.post('/api/v1/product-categories/create', data),

    // 更新分类
    updateCategory: (data) => api.post('/api/v1/product-categories/update', data),

    // 删除分类
    deleteCategory: (id) => api.post(`/api/v1/product-categories/delete/${id}`),
};

// 订单相关 API
export const orderAPI = {
    // 获取订单列表
    getOrders: (params) => api.get('/api/v1/orders', { params }),

    // 分页查询订单
    queryOrders: (data) => api.post('/api/v1/orders/query', data),

    // 获取订单详情
    getOrder: (id) => api.get(`/api/v1/orders/${id}`),

    // 创建订单
    createOrder: (data) => api.post('/api/v1/orders/create', data),

    // 更新订单状态
    updateOrderStatus: (data) => api.post('/api/v1/orders/update-status', data),

    // 更新订单支付状态
    updatePaymentStatus: (data) => api.post('/api/v1/orders/update-payment-status', data),

    // 删除订单
    deleteOrder: (id) => api.delete(`/api/v1/orders/${id}`),

    // 取消订单
    cancelOrder: (id) => api.patch(`/api/v1/orders/${id}/cancel`),
};

// 会员相关 API
export const memberAPI = {
    // 获取会员列表
    getMembers: (params) => api.get('/api/v1/members', { params }),

    // 获取会员详情
    getMember: (id) => api.get(`/api/v1/members/${id}`),

    // 创建会员
    createMember: (data) => api.post('/api/v1/members/create', data),

    // 更新会员信息
    updateMember: (data) => api.post('/api/v1/members/update', data),

    // 查询会员
    getMemberById: (id) => api.get(`/api/v1/members/${id}`),
    getMemberByMemberId: (memberId) => api.get(`/api/v1/members/by-member-id/${memberId}`),
    getMemberByPhone: (phoneNumber) => api.get(`/api/v1/members/by-phone?phoneNumber=${phoneNumber}`),
    getMemberByWxOpenid: (wxOpenid) => api.get(`/api/v1/members/by-wx-openid?wxOpenid=${wxOpenid}`),

    // 分页查询会员
    queryMembers: (data) => api.post('/api/v1/members/query', data),

    // 删除会员
    deleteMember: (id) => api.post(`/api/v1/members/delete/${id}`),

    // 更新会员余额
    updateBalance: (data) => api.post('/api/v1/members/update-balance', data),

    // 更新会员积分
    updatePoints: (data) => api.post('/api/v1/members/update-points', data),

    // 获取会员统计信息
    getStatistics: () => api.get('/api/v1/members/statistics'),
};

// 会员等级相关 API
export const membershipLevelAPI = {
    // 获取活跃的会员等级列表
    getActiveLevels: () => api.get('/api/v1/membership-levels/active'),

    // 获取所有会员等级
    getAllLevels: () => api.get('/api/v1/membership-levels'),

    // 获取会员等级详情
    getLevel: (id) => api.get(`/api/v1/membership-levels/${id}`),
};

// 库存相关 API
export const inventoryAPI = {
    // 获取库存列表
    getInventory: (params) => api.get('/api/v1/inventory', { params }),
    
    // 更新库存
    updateStock: (productId, quantity, type) => api.post(`/api/v1/inventory/${productId}/stock`, { quantity, type }),
    
    // 库存盘点
    stockTaking: (data) => api.post('/api/v1/inventory/stock-taking', data),
    
    // 获取库存预警
    getStockAlerts: () => api.get('/api/v1/inventory/alerts'),
};

// 报表相关 API
export const reportAPI = {
    // 销售报表
    getSalesReport: (params) => api.get('/api/v1/reports/sales', { params }),
    
    // 库存报表
    getInventoryReport: (params) => api.get('/api/v1/reports/inventory', { params }),
    
    // 会员分析
    getMemberAnalysis: (params) => api.get('/api/v1/reports/members', { params }),
    
    // 财务报表
    getFinancialReport: (params) => api.get('/api/v1/reports/financial', { params }),
    
    // 经营概览
    getDashboardData: () => api.get('/api/v1/reports/dashboard'),
};

// 系统设置相关 API
export const settingsAPI = {
    // 获取系统设置
    getSettings: () => api.get('/api/v1/settings'),
    
    // 更新系统设置
    updateSettings: (data) => api.put('/api/v1/settings', data),
    
    // 获取用户列表
    getUsers: () => api.get('/api/v1/settings/users'),
    
    // 创建用户
    createUser: (data) => api.post('/api/v1/settings/users', data),
    
    // 更新用户
    updateUser: (id, data) => api.put(`/api/v1/settings/users/${id}`, data),
    
    // 删除用户
    deleteUser: (id) => api.delete(`/api/v1/settings/users/${id}`),
};



// 会员余额消费记录相关 API
export const consumptionLogAPI = {
    // 创建消费记录
    createConsumptionLog: (data) => api.post('/api/v1/consumption-logs/create', data),

    // 查询消费记录
    getConsumptionLogById: (id) => api.get(`/api/v1/consumption-logs/${id}`),
    getConsumptionLogsByMember: (memberId) => api.get(`/api/v1/consumption-logs/by-member/${memberId}`),
    getConsumptionLogsByOrder: (orderId) => api.get(`/api/v1/consumption-logs/by-order/${orderId}`),

    // 分页查询消费记录
    queryConsumptionLogs: (data) => api.post('/api/v1/consumption-logs/query', data),

    // 统计信息
    getStatistics: () => api.get('/api/v1/consumption-logs/statistics'),
    getMemberStatistics: (memberId) => api.get(`/api/v1/consumption-logs/member-statistics/${memberId}`),
};

// 会员积分记录相关 API
export const pointsLogAPI = {
    // 创建积分记录
    createPointsLog: (data) => api.post('/api/v1/points-logs/create', data),

    // 查询积分记录
    getPointsLogById: (id) => api.get(`/api/v1/points-logs/${id}`),
    getPointsLogsByMember: (memberId) => api.get(`/api/v1/points-logs/by-member/${memberId}`),
    getPointsLogsByChangeType: (changeType) => api.get(`/api/v1/points-logs/by-change-type/${changeType}`),
    getPointsLogsByOrder: (orderId) => api.get(`/api/v1/points-logs/by-order/${orderId}`),

    // 分页查询积分记录
    queryPointsLogs: (data) => api.post('/api/v1/points-logs/query', data),

    // 统计信息
    getStatistics: () => api.get('/api/v1/points-logs/statistics'),
    getMemberStatistics: (memberId) => api.get(`/api/v1/points-logs/member-statistics/${memberId}`),

    // 测试接口
    getPointsChangeTypes: () => api.get('/api/v1/test/logs/points-change-types'),
    validatePointsChangeType: (code) => api.post(`/api/v1/test/logs/validate-points-change-type?code=${code}`),
    getPointsChangeCategories: () => api.get('/api/v1/test/logs/points-change-categories'),
    simulatePointsChange: (changeType, pointsChange, currentPoints) =>
        api.post(`/api/v1/test/logs/simulate-points-change?changeType=${changeType}&pointsChange=${pointsChange}&currentPoints=${currentPoints}`),
    getPointsChangeScenarios: () => api.get('/api/v1/test/logs/points-change-scenarios'),
};

// 支付相关 API
export const paymentAPI = {
    // 创建支付
    createPayment: (data) => api.post('/api/v1/payments', data),

    // 查询支付状态
    getPaymentStatus: (id) => api.get(`/api/v1/payments/${id}/status`),

    // 退款
    refund: (id, amount) => api.post(`/api/v1/payments/${id}/refund`, { amount }),
};

// 充值记录相关 API
export const rechargeLogAPI = {
    // 线下充值
    offlineRecharge: (data) => api.post('/api/v1/recharge-logs/offline-recharge', data),

    // 查询充值记录
    getRechargeLogById: (id) => api.get(`/api/v1/recharge-logs/${id}`),
    getRechargeLogsByMember: (memberId) => api.get(`/api/v1/recharge-logs/by-member/${memberId}`),

    // 分页查询充值记录
    queryRechargeLogs: (data) => api.post('/api/v1/recharge-logs/query', data),

    // 统计信息
    getStatistics: () => api.get('/api/v1/recharge-logs/statistics'),
    getMemberStatistics: (memberId) => api.get(`/api/v1/recharge-logs/member-statistics/${memberId}`),
};

// 桌台相关 API
export const tableAPI = {
    // 获取门店已激活桌台
    getTables: () => api.get('/api/v1/store-tables/store'),

    // 开桌
    openTable: (tableId, orderId) => api.post(`/api/v1/store-tables/${tableId}/open`, { orderId }),

    // 关桌
    closeTable: (tableId) => api.post(`/api/v1/store-tables/${tableId}/close`),

    // 分页查询桌台使用记录
    queryTableUsage: (data) => api.post('/api/v1/store-tables/usage/query', data),
};

// 统计相关 API
export const statisticsAPI = {
    // 获取今日业务统计数据
    getTodayStatistics: () => api.get('/api/v1/statistics/today'),

    // 获取最近5笔订单详情
    getRecentOrders: () => api.get('/api/v1/statistics/recent-orders'),
};

// 备忘录相关 API
export const memoAPI = {
    // 创建备忘录
    createMemo: (content) => api.post('/api/v1/memos/create', { content }),

    // 分页查询备忘录
    queryMemos: (data) => api.post('/api/v1/memos/query', data),

    // 修改备忘录状态
    updateStatus: (ids, status) => api.post('/api/v1/memos/update-status', { ids, status }),
};

// 文件上传相关 API
export const fileAPI = {
    // 上传文件
    uploadFile: (file) => {
        const formData = new FormData();
        formData.append('file', file);

        return api.post('/api/v1/file/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },
};

export default api;
