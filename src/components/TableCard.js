import React from 'react';

const TableCard = ({ table, onOpen, onClose, onViewUsage }) => {
    // 格式化时间
    const formatTime = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // 格式化时长
    const formatDuration = (minutes) => {
        if (!minutes || minutes < 0) return '0分钟';
        
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours > 0) {
            return `${hours}小时${mins}分钟`;
        }
        return `${mins}分钟`;
    };

    // 获取状态样式
    const getStatusStyle = () => {
        if (table.isOpen) {
            return {
                bgColor: 'bg-apple-green/10',
                textColor: 'text-apple-green',
                borderColor: 'border-apple-green/20',
                icon: 'fas fa-play-circle'
            };
        } else {
            return {
                bgColor: 'bg-apple-gray-100',
                textColor: 'text-apple-gray-600',
                borderColor: 'border-apple-gray-200',
                icon: 'fas fa-pause-circle'
            };
        }
    };

    const statusStyle = getStatusStyle();

    return (
        <div className={`apple-card rounded-2xl p-6 border-2 ${statusStyle.borderColor} transition-all duration-200 hover:shadow-lg`}>
            {/* 桌台标题和状态 */}
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                    <div className={`w-10 h-10 ${statusStyle.bgColor} rounded-xl flex items-center justify-center mr-3`}>
                        <i className={`${statusStyle.icon} ${statusStyle.textColor} text-lg`}></i>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-apple-gray-900">{table.tableNumber}</h3>
                        <p className={`text-sm ${statusStyle.textColor} font-medium`}>
                            {table.isOpen ? '使用中' : '空闲'}
                        </p>
                    </div>
                </div>
                
                {/* 二维码图标 */}
                {table.qrcodeUrl && (
                    <div className="w-8 h-8 bg-apple-gray-100 rounded-lg flex items-center justify-center">
                        <i className="fas fa-qrcode text-apple-gray-500"></i>
                    </div>
                )}
            </div>

            {/* 桌台信息 */}
            <div className="space-y-3 mb-6">
                {table.isOpen ? (
                    <>
                        {/* 开台时间 */}
                        {table.openedAt && (
                            <div className="flex items-center text-sm">
                                <i className="fas fa-clock text-apple-gray-400 w-4 mr-2"></i>
                                <span className="text-apple-gray-600">开台时间：</span>
                                <span className="text-apple-gray-900 ml-1">{formatTime(table.openedAt)}</span>
                            </div>
                        )}
                        
                        {/* 使用时长 */}
                        {table.currentDurationMinutes !== null && (
                            <div className="flex items-center text-sm">
                                <i className="fas fa-hourglass-half text-apple-gray-400 w-4 mr-2"></i>
                                <span className="text-apple-gray-600">使用时长：</span>
                                <span className="text-apple-green font-medium ml-1">
                                    {formatDuration(table.currentDurationMinutes)}
                                </span>
                            </div>
                        )}
                        
                        {/* 订单ID */}
                        {table.currentOrderId && (
                            <div className="flex items-center text-sm">
                                <i className="fas fa-receipt text-apple-gray-400 w-4 mr-2"></i>
                                <span className="text-apple-gray-600">订单号：</span>
                                <span className="text-apple-gray-900 ml-1">#{table.currentOrderId}</span>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="text-center py-4">
                        <i className="fas fa-coffee text-apple-gray-300 text-2xl mb-2"></i>
                        <p className="text-apple-gray-500 text-sm">桌台空闲，等待客人</p>
                    </div>
                )}
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-2">
                {table.isOpen ? (
                    <>
                        <button
                            onClick={onClose}
                            className="flex-1 flex items-center justify-center px-4 py-2 bg-apple-red text-white rounded-apple hover:bg-apple-red/90 transition-colors"
                        >
                            <i className="fas fa-stop mr-2"></i>
                            结账
                        </button>
                        <button
                            onClick={onViewUsage}
                            className="px-4 py-2 text-apple-blue border border-apple-blue rounded-apple hover:bg-apple-blue/5 transition-colors"
                        >
                            <i className="fas fa-history"></i>
                        </button>
                    </>
                ) : (
                    <>
                        <button
                            onClick={onOpen}
                            className="flex-1 flex items-center justify-center px-4 py-2 bg-apple-green text-white rounded-apple hover:bg-apple-green/90 transition-colors"
                        >
                            <i className="fas fa-play mr-2"></i>
                            开台
                        </button>
                        <button
                            onClick={onViewUsage}
                            className="px-4 py-2 text-apple-blue border border-apple-blue rounded-apple hover:bg-apple-blue/5 transition-colors"
                        >
                            <i className="fas fa-history"></i>
                        </button>
                    </>
                )}
            </div>

            {/* 桌台基本信息 */}
            <div className="mt-4 pt-4 border-t border-apple-gray-200">
                <div className="flex justify-between text-xs text-apple-gray-500">
                    <span>桌台ID: {table.id}</span>
                    <span>创建时间: {formatTime(table.createdAt)}</span>
                </div>
            </div>
        </div>
    );
};

export default TableCard;
