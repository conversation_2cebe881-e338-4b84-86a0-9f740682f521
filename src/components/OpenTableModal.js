import React, { useState } from 'react';

const OpenTableModal = ({ table, onClose, onConfirm }) => {
    const [orderId, setOrderId] = useState('');
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!orderId.trim()) {
            alert('请输入订单ID');
            return;
        }

        setLoading(true);
        try {
            await onConfirm(table.id, parseInt(orderId));
        } catch (error) {
            console.error('开台失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    return (
        <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={handleOverlayClick}
        >
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
                {/* 模态框头部 */}
                <div className="flex items-center justify-between p-6 border-b border-apple-gray-200">
                    <div className="flex items-center">
                        <div className="w-10 h-10 bg-apple-green/10 rounded-xl flex items-center justify-center mr-3">
                            <i className="fas fa-play text-apple-green text-lg"></i>
                        </div>
                        <div>
                            <h2 className="text-xl font-semibold text-apple-gray-900">开台</h2>
                            <p className="text-sm text-apple-gray-600">桌台 {table.tableNumber}</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-apple-gray-100 transition-colors"
                    >
                        <i className="fas fa-times text-apple-gray-500"></i>
                    </button>
                </div>

                {/* 模态框内容 */}
                <form onSubmit={handleSubmit} className="p-6">
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-apple-gray-900 mb-2">
                            订单ID <span className="text-apple-red">*</span>
                        </label>
                        <input
                            type="number"
                            value={orderId}
                            onChange={(e) => setOrderId(e.target.value)}
                            placeholder="请输入订单ID"
                            className="apple-input w-full px-4 py-3 rounded-apple border border-apple-gray-300 focus:border-apple-blue focus:ring-2 focus:ring-apple-blue/20"
                            required
                            min="1"
                            disabled={loading}
                        />
                        <p className="text-xs text-apple-gray-500 mt-2">
                            请输入有效的订单ID来开启桌台
                        </p>
                    </div>

                    {/* 桌台信息确认 */}
                    <div className="bg-apple-gray-50 rounded-xl p-4 mb-6">
                        <h3 className="text-sm font-medium text-apple-gray-900 mb-3">桌台信息</h3>
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span className="text-apple-gray-600">桌台号码：</span>
                                <span className="text-apple-gray-900 font-medium">{table.tableNumber}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span className="text-apple-gray-600">桌台ID：</span>
                                <span className="text-apple-gray-900">{table.id}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span className="text-apple-gray-600">当前状态：</span>
                                <span className="text-apple-gray-500">空闲</span>
                            </div>
                        </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-3">
                        <button
                            type="button"
                            onClick={onClose}
                            className="flex-1 px-4 py-3 text-apple-gray-700 bg-apple-gray-100 rounded-apple hover:bg-apple-gray-200 transition-colors"
                            disabled={loading}
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            className="flex-1 px-4 py-3 bg-apple-green text-white rounded-apple hover:bg-apple-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    开台中...
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-play mr-2"></i>
                                    确认开台
                                </>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default OpenTableModal;
