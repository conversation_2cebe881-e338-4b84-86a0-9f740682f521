import React, { useState } from 'react';
import { rechargeLogAPI } from '../services/api';
import { safeMemberId } from '../utils/numberUtils';

const BalanceModal = ({ visible, member, onClose, onSuccess }) => {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        amount: '',
        paymentMethod: 'cash',
        remark: '',
        paymentTransactionId: ''
    });
    const [errors, setErrors] = useState({});

    // 预设金额选项
    const presetAmounts = [50, 100, 200, 500, 1000];

    // 支付方式选项
    const paymentMethods = [
        { value: 'cash', label: '现金支付', description: '线下现金支付' },
        { value: 'offline_scan', label: '线下扫码', description: '线下扫码支付（支付宝、微信等）' }
    ];

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // 清除对应字段的错误
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handlePresetAmount = (amount) => {
        setFormData(prev => ({
            ...prev,
            amount: amount.toString()
        }));
        
        if (errors.amount) {
            setErrors(prev => ({
                ...prev,
                amount: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.amount || formData.amount <= 0) {
            newErrors.amount = '请输入有效的充值金额';
        } else if (isNaN(formData.amount)) {
            newErrors.amount = '请输入数字';
        } else if (parseFloat(formData.amount) > 10000) {
            newErrors.amount = '单次充值金额不能超过10000元';
        }

        if (!formData.paymentMethod) {
            newErrors.paymentMethod = '请选择支付方式';
        }

        if (!formData.remark.trim()) {
            newErrors.remark = '请输入充值备注';
        }

        // 如果是线下扫码支付，需要交易号
        if (formData.paymentMethod === 'offline_scan' && !formData.paymentTransactionId.trim()) {
            newErrors.paymentTransactionId = '线下扫码支付需要输入交易号';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            const requestData = {
                memberId: safeMemberId(member.memberId), // 安全处理 memberId
                amount: parseFloat(formData.amount),
                paymentMethod: formData.paymentMethod,
                remark: formData.remark,
                paymentTransactionId: formData.paymentMethod === 'offline_scan' ? formData.paymentTransactionId : null
            };

            const response = await rechargeLogAPI.offlineRecharge(requestData);

            if (response.data.code === 200) {
                onSuccess();
                // 重置表单
                setFormData({
                    amount: '',
                    paymentMethod: 'cash',
                    remark: '',
                    paymentTransactionId: ''
                });
                setErrors({});
            } else {
                alert(response.data.msg || '充值失败');
            }
        } catch (error) {
            console.error('充值失败:', error);
            alert('充值失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setFormData({
            amount: '',
            paymentMethod: 'cash',
            remark: '',
            paymentTransactionId: ''
        });
        setErrors({});
        onClose();
    };

    if (!visible || !member) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                {/* 背景遮罩 */}
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                    onClick={handleClose}
                ></div>

                {/* 模态框内容 */}
                <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
                    <form onSubmit={handleSubmit}>
                        {/* 头部 */}
                        <div className="bg-white px-6 py-4 border-b border-apple-gray-200">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-apple-gray-900">
                                    会员充值
                                </h3>
                                <button
                                    type="button"
                                    onClick={handleClose}
                                    className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                                >
                                    <i className="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        {/* 会员信息 */}
                        <div className="bg-apple-gray-50 px-6 py-4">
                            <div className="flex items-center">
                                <div className="flex-shrink-0 h-12 w-12">
                                    {member.avatarUrl ? (
                                        <img
                                            className="h-12 w-12 rounded-full"
                                            src={member.avatarUrl}
                                            alt={member.nickname}
                                        />
                                    ) : (
                                        <div className="h-12 w-12 rounded-full bg-apple-gray-300 flex items-center justify-center">
                                            <i className="fas fa-user text-apple-gray-600 text-lg"></i>
                                        </div>
                                    )}
                                </div>
                                <div className="ml-4">
                                    <div className="text-sm font-medium text-apple-gray-900">
                                        {member.nickname || '未设置'}
                                    </div>
                                    <div className="text-sm text-apple-gray-500">
                                        {member.phoneNumber || '未绑定手机'}
                                    </div>
                                    <div className="text-sm text-apple-gray-600">
                                        当前余额: <span className="font-medium text-green-600">¥{Number(member.balance || 0).toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 表单内容 */}
                        <div className="bg-white px-6 py-4 space-y-4">
                            {/* 充值金额 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    充值金额 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="number"
                                    name="amount"
                                    value={formData.amount}
                                    onChange={handleInputChange}
                                    min="0.01"
                                    step="0.01"
                                    placeholder="请输入充值金额"
                                    className={`apple-input w-full px-3 py-2 rounded-apple ${errors.amount ? 'border-red-300' : ''}`}
                                />
                                {errors.amount && (
                                    <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
                                )}
                            </div>

                            {/* 快捷金额 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    快捷金额
                                </label>
                                <div className="grid grid-cols-5 gap-2">
                                    {presetAmounts.map(amount => (
                                        <button
                                            key={amount}
                                            type="button"
                                            onClick={() => handlePresetAmount(amount)}
                                            className={`px-3 py-2 text-sm rounded-apple border transition-colors ${
                                                formData.amount === amount.toString()
                                                    ? 'bg-apple-blue text-white border-apple-blue'
                                                    : 'bg-white text-apple-gray-700 border-apple-gray-300 hover:bg-apple-gray-50'
                                            }`}
                                        >
                                            ¥{amount}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* 支付方式 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    支付方式 <span className="text-red-500">*</span>
                                </label>
                                <div className="space-y-2">
                                    {paymentMethods.map(method => (
                                        <label key={method.value} className="flex items-center">
                                            <input
                                                type="radio"
                                                name="paymentMethod"
                                                value={method.value}
                                                checked={formData.paymentMethod === method.value}
                                                onChange={handleInputChange}
                                                className="mr-3 text-apple-blue focus:ring-apple-blue"
                                            />
                                            <div>
                                                <div className="text-sm font-medium text-apple-gray-900">
                                                    {method.label}
                                                </div>
                                                <div className="text-xs text-apple-gray-500">
                                                    {method.description}
                                                </div>
                                            </div>
                                        </label>
                                    ))}
                                </div>
                                {errors.paymentMethod && (
                                    <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
                                )}
                            </div>

                            {/* 交易号 - 仅在线下扫码时显示 */}
                            {formData.paymentMethod === 'offline_scan' && (
                                <div>
                                    <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                        交易号 <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        name="paymentTransactionId"
                                        value={formData.paymentTransactionId}
                                        onChange={handleInputChange}
                                        placeholder="请输入支付宝/微信交易号"
                                        className={`apple-input w-full px-3 py-2 rounded-apple ${errors.paymentTransactionId ? 'border-red-300' : ''}`}
                                    />
                                    {errors.paymentTransactionId && (
                                        <p className="mt-1 text-sm text-red-600">{errors.paymentTransactionId}</p>
                                    )}
                                </div>
                            )}

                            {/* 充值备注 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    充值备注 <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    name="remark"
                                    value={formData.remark}
                                    onChange={handleInputChange}
                                    rows="3"
                                    placeholder="请输入充值备注信息"
                                    className={`apple-input w-full px-3 py-2 rounded-apple resize-none ${errors.remark ? 'border-red-300' : ''}`}
                                />
                                {errors.remark && (
                                    <p className="mt-1 text-sm text-red-600">{errors.remark}</p>
                                )}
                            </div>

                            {/* 充值后余额预览 */}
                            {formData.amount && !isNaN(formData.amount) && formData.amount > 0 && (
                                <div className="bg-green-50 border border-green-200 rounded-apple p-3">
                                    <div className="flex items-center">
                                        <i className="fas fa-info-circle text-green-600 mr-2"></i>
                                        <span className="text-sm text-green-800">
                                            充值后余额: ¥{(Number(member.balance || 0) + Number(formData.amount)).toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* 底部按钮 */}
                        <div className="bg-apple-gray-50 px-6 py-4 flex justify-end space-x-3">
                            <button
                                type="button"
                                onClick={handleClose}
                                className="bg-white text-apple-gray-700 px-4 py-2 rounded-apple border border-apple-gray-300 hover:bg-apple-gray-50 transition-colors"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                disabled={loading}
                                className="apple-button px-4 py-2 rounded-apple disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? (
                                    <>
                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                        充值中...
                                    </>
                                ) : (
                                    <>
                                        <i className="fas fa-plus-circle mr-2"></i>
                                        确认充值
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default BalanceModal;
