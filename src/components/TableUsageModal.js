import React, { useState, useEffect } from 'react';
import { tableAPI } from '../services/api';

const TableUsageModal = ({ table, onClose }) => {
    const [records, setRecords] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });
    const [filters, setFilters] = useState({
        startTime: '',
        endTime: '',
        status: ''
    });

    // 获取使用记录
    const fetchRecords = async (page = 1) => {
        try {
            setLoading(true);
            setError(null);

            const queryData = {
                current: page,
                size: pagination.size,
                tableId: table?.id || null,
                ...filters
            };

            const response = await tableAPI.queryTableUsage(queryData);
            
            if (response.data.code === 200) {
                const data = response.data.data;
                setRecords(data.records || []);
                setPagination({
                    current: data.current,
                    size: data.size,
                    total: data.total,
                    pages: data.pages
                });
            } else {
                setError(response.data.msg || '获取使用记录失败');
            }
        } catch (err) {
            console.error('获取使用记录失败:', err);
            setError('获取使用记录失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 格式化时间
    const formatDateTime = (dateString) => {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // 格式化时长
    const formatDuration = (minutes) => {
        if (!minutes || minutes < 0) return '-';
        
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours > 0) {
            return `${hours}小时${mins}分钟`;
        }
        return `${mins}分钟`;
    };

    // 获取状态样式
    const getStatusStyle = (status) => {
        switch (status) {
            case 'COMPLETED':
                return {
                    bgColor: 'bg-apple-green/10',
                    textColor: 'text-apple-green',
                    icon: 'fas fa-check-circle'
                };
            case 'IN_PROGRESS':
                return {
                    bgColor: 'bg-apple-blue/10',
                    textColor: 'text-apple-blue',
                    icon: 'fas fa-clock'
                };
            default:
                return {
                    bgColor: 'bg-apple-gray-100',
                    textColor: 'text-apple-gray-600',
                    icon: 'fas fa-question-circle'
                };
        }
    };

    // 处理筛选
    const handleFilter = () => {
        fetchRecords(1);
    };

    // 重置筛选
    const handleResetFilter = () => {
        setFilters({
            startTime: '',
            endTime: '',
            status: ''
        });
        setTimeout(() => fetchRecords(1), 0);
    };

    // 翻页
    const handlePageChange = (page) => {
        fetchRecords(page);
    };

    // 处理遮罩点击
    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    useEffect(() => {
        fetchRecords();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    return (
        <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={handleOverlayClick}
        >
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
                {/* 模态框头部 */}
                <div className="flex items-center justify-between p-6 border-b border-apple-gray-200">
                    <div className="flex items-center">
                        <div className="w-10 h-10 bg-apple-blue/10 rounded-xl flex items-center justify-center mr-3">
                            <i className="fas fa-history text-apple-blue text-lg"></i>
                        </div>
                        <div>
                            <h2 className="text-xl font-semibold text-apple-gray-900">使用记录</h2>
                            <p className="text-sm text-apple-gray-600">
                                {table ? `桌台 ${table.tableNumber}` : '所有桌台'}
                            </p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-apple-gray-100 transition-colors"
                    >
                        <i className="fas fa-times text-apple-gray-500"></i>
                    </button>
                </div>

                {/* 筛选区域 */}
                <div className="p-6 border-b border-apple-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-1">开始时间</label>
                            <input
                                type="datetime-local"
                                value={filters.startTime}
                                onChange={(e) => setFilters(prev => ({ ...prev, startTime: e.target.value }))}
                                className="w-full px-3 py-2 border border-apple-gray-300 rounded-apple text-sm focus:border-apple-blue focus:ring-2 focus:ring-apple-blue/20"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-1">结束时间</label>
                            <input
                                type="datetime-local"
                                value={filters.endTime}
                                onChange={(e) => setFilters(prev => ({ ...prev, endTime: e.target.value }))}
                                className="w-full px-3 py-2 border border-apple-gray-300 rounded-apple text-sm focus:border-apple-blue focus:ring-2 focus:ring-apple-blue/20"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-1">状态</label>
                            <select
                                value={filters.status}
                                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                                className="w-full px-3 py-2 border border-apple-gray-300 rounded-apple text-sm focus:border-apple-blue focus:ring-2 focus:ring-apple-blue/20"
                            >
                                <option value="">全部状态</option>
                                <option value="COMPLETED">已完成</option>
                                <option value="ACTIVE">进行中</option>
                            </select>
                        </div>
                        <div className="flex items-end space-x-2">
                            <button
                                onClick={handleFilter}
                                className="flex-1 px-4 py-2 bg-apple-blue text-white rounded-apple hover:bg-apple-blue/90 transition-colors text-sm"
                            >
                                筛选
                            </button>
                            <button
                                onClick={handleResetFilter}
                                className="px-4 py-2 text-apple-gray-600 border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 transition-colors text-sm"
                            >
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                {/* 内容区域 */}
                <div className="flex-1 overflow-hidden">
                    {loading ? (
                        <div className="flex items-center justify-center h-64">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue mx-auto mb-4"></div>
                                <p className="text-apple-gray-600">加载中...</p>
                            </div>
                        </div>
                    ) : error ? (
                        <div className="flex items-center justify-center h-64">
                            <div className="text-center">
                                <i className="fas fa-exclamation-triangle text-4xl text-apple-red mb-4"></i>
                                <p className="text-apple-gray-600">{error}</p>
                                <button 
                                    onClick={() => fetchRecords()}
                                    className="mt-4 px-4 py-2 bg-apple-blue text-white rounded-apple hover:bg-apple-blue/90 transition-colors"
                                >
                                    重新加载
                                </button>
                            </div>
                        </div>
                    ) : records.length === 0 ? (
                        <div className="flex items-center justify-center h-64">
                            <div className="text-center">
                                <i className="fas fa-inbox text-4xl text-apple-gray-300 mb-4"></i>
                                <p className="text-apple-gray-600">暂无使用记录</p>
                            </div>
                        </div>
                    ) : (
                        <div className="overflow-auto h-full">
                            <table className="w-full">
                                <thead className="bg-apple-gray-50 sticky top-0">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">状态</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">开台时间</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">关台时间</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">使用时长</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">订单ID</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-apple-gray-200">
                                    {records.map((record) => {
                                        const statusStyle = getStatusStyle(record.status);
                                        return (
                                            <tr key={record.id} className="hover:bg-apple-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className={`w-8 h-8 ${statusStyle.bgColor} rounded-lg flex items-center justify-center mr-3`}>
                                                            <i className={`${statusStyle.icon} ${statusStyle.textColor} text-sm`}></i>
                                                        </div>
                                                        <div>
                                                            <div className={`text-sm font-medium ${statusStyle.textColor}`}>
                                                                {record.statusDescription}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {formatDateTime(record.openedAt)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {formatDateTime(record.closedAt)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {formatDuration(record.durationMinutes)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {record.orderId || '-'}
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>

                {/* 分页区域 */}
                {!loading && !error && records.length > 0 && (
                    <div className="p-6 border-t border-apple-gray-200">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-apple-gray-700">
                                显示 {((pagination.current - 1) * pagination.size) + 1} 到{' '}
                                {Math.min(pagination.current * pagination.size, pagination.total)} 条，
                                共 {pagination.total} 条记录
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handlePageChange(pagination.current - 1)}
                                    disabled={pagination.current <= 1}
                                    className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    上一页
                                </button>
                                <span className="px-3 py-1 text-sm text-apple-gray-700">
                                    {pagination.current} / {pagination.pages}
                                </span>
                                <button
                                    onClick={() => handlePageChange(pagination.current + 1)}
                                    disabled={pagination.current >= pagination.pages}
                                    className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TableUsageModal;
