import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
    const { isAuthenticated, loading } = useAuth();
    const location = useLocation();

    // 如果正在加载，显示加载状态
    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-apple-gray-50">
                <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse">
                        <i className="fas fa-leaf text-white text-2xl"></i>
                    </div>
                    <h2 className="text-lg font-semibold text-apple-gray-900 mb-2">茶馆收银系统</h2>
                    <p className="text-apple-gray-600">正在加载...</p>
                </div>
            </div>
        );
    }

    // 如果未认证，重定向到登录页面
    if (!isAuthenticated) {
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    // 如果已认证，渲染子组件
    return children;
};

export default ProtectedRoute;
