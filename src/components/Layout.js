import React, { useState } from 'react';
import Sidebar from './Sidebar';

const Layout = ({ children, title, subtitle }) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);

    const toggleSidebar = () => {
        setSidebarOpen(!sidebarOpen);
    };

    const closeSidebar = () => {
        setSidebarOpen(false);
    };

    return (
        <div className="min-h-screen bg-apple-gray-50">
            {/* 侧边栏 */}
            <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />

            {/* 主要内容区域 */}
            <div className="lg:ml-80">
                {/* 顶部标题栏 */}
                <header className="bg-white/80 backdrop-blur-[20px] border-b border-gray-200/50 sticky top-0 z-20">
                    <div className="px-6 py-4">
                        <div className="flex items-center justify-between">
                            {/* 移动端菜单按钮 */}
                            <button 
                                onClick={toggleSidebar}
                                className="lg:hidden p-2 text-apple-gray-600 hover:text-apple-gray-900 transition-colors rounded-apple hover:bg-apple-gray-100"
                            >
                                <i className="fas fa-bars text-lg"></i>
                            </button>
                            
                            {/* 页面标题 */}
                            <div className="flex-1 lg:flex-none">
                                <h2 className="text-xl font-semibold text-apple-gray-900 font-sf-pro">
                                    {title}
                                </h2>
                                {subtitle && (
                                    <p className="text-sm text-apple-gray-500">{subtitle}</p>
                                )}
                            </div>

                            {/* 右侧操作区 */}
                            <div className="flex items-center space-x-4">
                                <button className="p-2 text-apple-gray-400 hover:text-apple-gray-600 transition-colors rounded-apple hover:bg-apple-gray-100">
                                    <i className="fas fa-bell text-lg"></i>
                                </button>
                                <button className="p-2 text-apple-gray-400 hover:text-apple-gray-600 transition-colors rounded-apple hover:bg-apple-gray-100">
                                    <i className="fas fa-search text-lg"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </header>

                {/* 主要内容 */}
                <main className="p-6">
                    {children}
                </main>
            </div>
        </div>
    );
};

export default Layout;
