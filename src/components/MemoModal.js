import React, { useState, useEffect } from 'react';
import { memoAPI } from '../services/api';

const MemoModal = ({ isOpen, onClose }) => {
    const [memos, setMemos] = useState([]);
    const [newMemo, setNewMemo] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });

    // 获取备忘录列表
    const fetchMemos = async (page = 1) => {
        try {
            setLoading(true);
            setError(null);

            const response = await memoAPI.queryMemos({
                current: page,
                size: pagination.size
            });

            if (response.data.code === 200) {
                const data = response.data.data;
                setMemos(data.records || []);
                setPagination({
                    current: data.current,
                    size: data.size,
                    total: data.total,
                    pages: data.pages
                });
            } else {
                setError(response.data.msg || '获取备忘录失败');
            }
        } catch (err) {
            console.error('获取备忘录失败:', err);
            setError('获取备忘录失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 组件挂载时获取数据
    useEffect(() => {
        if (isOpen) {
            fetchMemos();
        }
    }, [isOpen]); // eslint-disable-line react-hooks/exhaustive-deps

    // 添加新备忘录
    const addMemo = async () => {
        if (!newMemo.trim()) return;

        try {
            setLoading(true);
            const response = await memoAPI.createMemo(newMemo.trim());

            if (response.data.code === 200) {
                setNewMemo('');
                // 重新获取第一页数据
                await fetchMemos(1);
            } else {
                setError(response.data.msg || '创建备忘录失败');
            }
        } catch (err) {
            console.error('创建备忘录失败:', err);
            setError('创建备忘录失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 切换完成状态
    const toggleMemo = async (id, currentStatus) => {
        try {
            setLoading(true);
            const newStatus = currentStatus === 'DONE' ? 'PENDING' : 'DONE';

            const response = await memoAPI.updateStatus([id], newStatus);

            if (response.data.code === 200) {
                // 重新获取当前页数据
                await fetchMemos(pagination.current);
            } else {
                setError(response.data.msg || '更新状态失败');
            }
        } catch (err) {
            console.error('更新状态失败:', err);
            setError('更新状态失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 翻页
    const handlePageChange = (page) => {
        fetchMemos(page);
    };

    // 格式化时间
    const formatTime = (dateString) => {
        if (!dateString) return '';

        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;

        return date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // 处理遮罩点击
    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    if (!isOpen) return null;

    return (
        <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={handleOverlayClick}
        >
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[80vh] flex flex-col">
                {/* 模态框头部 */}
                <div className="flex items-center justify-between p-6 border-b border-apple-gray-200">
                    <div className="flex items-center">
                        <div className="w-10 h-10 bg-apple-blue/10 rounded-xl flex items-center justify-center mr-3">
                            <i className="fas fa-sticky-note text-apple-blue text-lg"></i>
                        </div>
                        <div>
                            <h2 className="text-xl font-semibold text-apple-gray-900">备忘录</h2>
                            <p className="text-sm text-apple-gray-600">记录重要事项</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-apple-gray-100 transition-colors"
                    >
                        <i className="fas fa-times text-apple-gray-500"></i>
                    </button>
                </div>

                {/* 添加新备忘录 */}
                <div className="p-6 border-b border-apple-gray-200">
                    <div className="flex space-x-3">
                        <input
                            type="text"
                            value={newMemo}
                            onChange={(e) => setNewMemo(e.target.value)}
                            placeholder="添加新的备忘录..."
                            className="flex-1 px-4 py-2 border border-apple-gray-300 rounded-apple focus:border-apple-blue focus:ring-2 focus:ring-apple-blue/20 text-sm"
                            onKeyPress={(e) => e.key === 'Enter' && !loading && addMemo()}
                            disabled={loading}
                        />
                        <button
                            onClick={addMemo}
                            disabled={loading || !newMemo.trim()}
                            className="px-4 py-2 bg-apple-blue text-white rounded-apple hover:bg-apple-blue/90 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {loading ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                                <i className="fas fa-plus"></i>
                            )}
                        </button>
                    </div>
                    {error && (
                        <div className="mt-3 p-3 bg-apple-red/10 border border-apple-red/20 rounded-apple">
                            <p className="text-sm text-apple-red">{error}</p>
                        </div>
                    )}
                </div>

                {/* 备忘录列表 */}
                <div className="flex-1 overflow-auto">
                    {loading && memos.length === 0 ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue mx-auto mb-4"></div>
                                <p className="text-apple-gray-600">加载中...</p>
                            </div>
                        </div>
                    ) : memos.length === 0 ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="text-center">
                                <i className="fas fa-sticky-note text-4xl text-apple-gray-300 mb-2"></i>
                                <p className="text-apple-gray-500 text-sm">暂无备忘录</p>
                            </div>
                        </div>
                    ) : (
                        <div className="p-6 space-y-3">
                            {memos.map((memo) => (
                                <div
                                    key={memo.id}
                                    className={`p-4 rounded-xl border transition-all duration-200 ${
                                        memo.status === 'DONE'
                                            ? 'bg-apple-gray-50 border-apple-gray-200'
                                            : 'bg-white border-apple-gray-300 hover:border-apple-blue/50'
                                    }`}
                                >
                                    <div className="flex items-start space-x-3">
                                        <button
                                            onClick={() => toggleMemo(memo.id, memo.status)}
                                            disabled={loading}
                                            className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 transition-colors disabled:opacity-50 ${
                                                memo.status === 'DONE'
                                                    ? 'bg-apple-green border-apple-green text-white'
                                                    : 'border-apple-gray-300 hover:border-apple-blue'
                                            }`}
                                        >
                                            {memo.status === 'DONE' && <i className="fas fa-check text-xs"></i>}
                                        </button>

                                        <div className="flex-1">
                                            <p
                                                className={`text-sm ${
                                                    memo.status === 'DONE'
                                                        ? 'text-apple-gray-500 line-through'
                                                        : 'text-apple-gray-900'
                                                }`}
                                            >
                                                {memo.content}
                                            </p>
                                            <div className="flex items-center justify-between mt-1">
                                                <p className="text-xs text-apple-gray-400">
                                                    创建于 {formatTime(memo.createdAt)}
                                                </p>
                                                {memo.updatedAt && memo.updatedAt !== memo.createdAt && (
                                                    <p className="text-xs text-apple-gray-400">
                                                        更新于 {formatTime(memo.updatedAt)}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* 分页和统计 */}
                {!loading && pagination.total > 0 && (
                    <div className="p-6 border-t border-apple-gray-200">
                        <div className="flex items-center justify-between mb-4">
                            <div className="text-sm text-apple-gray-600">
                                显示 {((pagination.current - 1) * pagination.size) + 1} 到{' '}
                                {Math.min(pagination.current * pagination.size, pagination.total)} 条，
                                共 {pagination.total} 条记录
                            </div>
                            <div className="text-sm text-apple-gray-600">
                                已完成: {memos.filter(m => m.status === 'DONE').length} 条
                            </div>
                        </div>

                        {/* 分页控件 */}
                        {pagination.pages > 1 && (
                            <div className="flex items-center justify-center space-x-2">
                                <button
                                    onClick={() => handlePageChange(pagination.current - 1)}
                                    disabled={pagination.current <= 1 || loading}
                                    className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    上一页
                                </button>

                                <div className="flex items-center space-x-1">
                                    {Array.from({ length: Math.min(pagination.pages, 5) }, (_, i) => {
                                        let pageNum;
                                        if (pagination.pages <= 5) {
                                            pageNum = i + 1;
                                        } else if (pagination.current <= 3) {
                                            pageNum = i + 1;
                                        } else if (pagination.current >= pagination.pages - 2) {
                                            pageNum = pagination.pages - 4 + i;
                                        } else {
                                            pageNum = pagination.current - 2 + i;
                                        }

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                disabled={loading}
                                                className={`w-8 h-8 text-sm rounded-apple transition-colors disabled:opacity-50 ${
                                                    pageNum === pagination.current
                                                        ? 'bg-apple-blue text-white'
                                                        : 'border border-apple-gray-300 hover:bg-apple-gray-50'
                                                }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}
                                </div>

                                <button
                                    onClick={() => handlePageChange(pagination.current + 1)}
                                    disabled={pagination.current >= pagination.pages || loading}
                                    className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    下一页
                                </button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default MemoModal;
