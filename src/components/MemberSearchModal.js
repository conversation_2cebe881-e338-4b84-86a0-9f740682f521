import React, { useState } from 'react';
import { memberAPI } from '../services/api';

const MemberSearchModal = ({ visible, onClose, onSelectMember }) => {
    const [searchPhone, setSearchPhone] = useState('');
    const [searchResult, setSearchResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSearch = async () => {
        if (!searchPhone.trim()) {
            setError('请输入手机号');
            return;
        }

        if (!/^1[3-9]\d{9}$/.test(searchPhone)) {
            setError('请输入正确的手机号格式');
            return;
        }

        setLoading(true);
        setError('');
        setSearchResult(null);

        try {
            const response = await memberAPI.getMemberByPhone(searchPhone);
            if (response.data.code === 200) {
                if (response.data.data) {
                    setSearchResult(response.data.data);
                } else {
                    setError('未找到该手机号对应的会员');
                }
            } else {
                setError(response.data.msg || '查询失败');
            }
        } catch (error) {
            console.error('查询会员失败:', error);
            setError('查询失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handleSelectMember = () => {
        if (searchResult && onSelectMember) {
            onSelectMember(searchResult);
            handleClose();
        }
    };

    const handleClose = () => {
        setSearchPhone('');
        setSearchResult(null);
        setError('');
        if (onClose) {
            onClose();
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    if (!visible) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-apple-gray-900">查找会员</h3>
                    <button
                        onClick={handleClose}
                        className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                    >
                        <i className="fas fa-times text-xl"></i>
                    </button>
                </div>

                {/* 搜索输入 */}
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            手机号
                        </label>
                        <div className="flex space-x-2">
                            <input
                                type="tel"
                                value={searchPhone}
                                onChange={(e) => setSearchPhone(e.target.value)}
                                onKeyPress={handleKeyPress}
                                placeholder="请输入会员手机号"
                                className="flex-1 px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                maxLength={11}
                            />
                            <button
                                onClick={handleSearch}
                                disabled={loading}
                                className="px-4 py-2 bg-apple-blue text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {loading ? (
                                    <i className="fas fa-spinner fa-spin"></i>
                                ) : (
                                    <i className="fas fa-search"></i>
                                )}
                            </button>
                        </div>
                    </div>

                    {/* 错误信息 */}
                    {error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-sm text-red-600">
                                <i className="fas fa-exclamation-circle mr-2"></i>
                                {error}
                            </p>
                        </div>
                    )}

                    {/* 搜索结果 */}
                    {searchResult && (
                        <div className="p-4 bg-apple-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-3">
                                <h4 className="font-medium text-apple-gray-900">会员信息</h4>
                                <span className="text-xs text-apple-gray-500">
                                    ID: {searchResult.memberId}
                                </span>
                            </div>
                            
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">姓名:</span>
                                    <span className="text-apple-gray-900">{searchResult.nickname || '未设置'}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">手机:</span>
                                    <span className="text-apple-gray-900">{searchResult.phoneNumber}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">等级:</span>
                                    <span className="text-apple-gray-900">{searchResult.membershipLevelName || '普通会员'}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">余额:</span>
                                    <span className="text-apple-blue font-medium">¥{searchResult.balance || 0}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">积分:</span>
                                    <span className="text-orange-600 font-medium">{searchResult.points || 0}</span>
                                </div>
                            </div>

                            <button
                                onClick={handleSelectMember}
                                className="w-full mt-4 py-2 bg-apple-blue text-white rounded-lg hover:bg-blue-600 transition-colors"
                            >
                                选择此会员
                            </button>
                        </div>
                    )}
                </div>

                {/* 底部按钮 */}
                <div className="flex justify-end space-x-3 mt-6">
                    <button
                        onClick={handleClose}
                        className="px-4 py-2 text-apple-gray-600 hover:text-apple-gray-800 transition-colors"
                    >
                        取消
                    </button>
                </div>
            </div>
        </div>
    );
};

export default MemberSearchModal;
