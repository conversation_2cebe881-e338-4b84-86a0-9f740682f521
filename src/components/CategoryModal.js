import React, { useState, useEffect } from 'react';
import { categoryAPI } from '../services/api';

const CategoryModal = ({ visible, onClose, onSuccess, category = null, type = 'create' }) => {
    const [formData, setFormData] = useState({
        name: '',
        parentId: 0,
        sortOrder: 1
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (visible) {
            if (category && type === 'edit') {
                setFormData({
                    id: category.id,
                    name: category.name || '',
                    parentId: category.parentId || 0,
                    sortOrder: category.sortOrder || 1
                });
            } else {
                setFormData({
                    name: '',
                    parentId: 0,
                    sortOrder: 1
                });
            }
            setErrors({});
        }
    }, [visible, category, type]);

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.name.trim()) {
            newErrors.name = '分类名称不能为空';
        }
        
        if (formData.sortOrder < 0) {
            newErrors.sortOrder = '排序值不能为负数';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: name === 'sortOrder' ? parseInt(value) || 0 : value
        }));
        
        // 清除对应字段的错误
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            let response;
            if (type === 'create') {
                response = await categoryAPI.createCategory(formData);
            } else if (type === 'edit') {
                response = await categoryAPI.updateCategory(formData);
            }

            if (response.data.code === 200) {
                onSuccess();
                onClose();
            } else {
                alert(response.data.msg || '操作失败');
            }
        } catch (error) {
            console.error('分类操作失败:', error);
            alert('操作失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    if (!visible) return null;

    const modalTitle = type === 'create' ? '新增分类' : '编辑分类';

    return (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-hidden">
                {/* 标题栏 */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 className="text-xl font-semibold text-apple-gray-900">{modalTitle}</h3>
                    <button
                        onClick={onClose}
                        className="p-2 text-apple-gray-400 hover:text-apple-gray-600 transition-colors rounded-apple hover:bg-apple-gray-100"
                    >
                        <i className="fas fa-times text-lg"></i>
                    </button>
                </div>

                {/* 表单内容 */}
                <form onSubmit={handleSubmit} className="p-6 space-y-4">
                    {/* 分类名称 */}
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            分类名称 <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            placeholder="请输入分类名称"
                            className={`apple-input w-full px-4 py-3 rounded-apple ${
                                errors.name ? 'border-red-500 focus:border-red-500' : ''
                            }`}
                        />
                        {errors.name && (
                            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                        )}
                    </div>

                    {/* 排序值 */}
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            排序值
                        </label>
                        <input
                            type="number"
                            name="sortOrder"
                            value={formData.sortOrder}
                            onChange={handleInputChange}
                            placeholder="请输入排序值"
                            min="0"
                            className={`apple-input w-full px-4 py-3 rounded-apple ${
                                errors.sortOrder ? 'border-red-500 focus:border-red-500' : ''
                            }`}
                        />
                        {errors.sortOrder && (
                            <p className="text-red-500 text-sm mt-1">{errors.sortOrder}</p>
                        )}
                        <p className="text-apple-gray-500 text-sm mt-1">数值越小排序越靠前</p>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="flex-1 px-4 py-3 text-apple-gray-700 bg-apple-gray-100 rounded-apple hover:bg-apple-gray-200 transition-colors"
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="flex-1 px-4 py-3 bg-apple-blue text-white rounded-apple hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {loading ? (
                                <div className="flex items-center justify-center">
                                    <i className="fas fa-spinner fa-spin mr-2"></i>
                                    处理中...
                                </div>
                            ) : (
                                type === 'create' ? '创建' : '保存'
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default CategoryModal;
