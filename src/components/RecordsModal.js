import React, { useState, useEffect, useCallback } from 'react';
import { consumptionLogAPI, pointsLogAPI } from '../services/api';
import { safeMemberId } from '../utils/numberUtils';

const RecordsModal = ({ visible, type, member, onClose }) => {
    const [loading, setLoading] = useState(false);
    const [records, setRecords] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });

    // 获取记录数据
    const fetchRecords = useCallback(async () => {
        if (!member || !type) return;

        setLoading(true);
        try {
            let response;
            const queryData = {
                current: pagination.current,
                size: pagination.size,
                memberId: safeMemberId(member.memberId) // 安全处理 memberId
            };

            if (type === 'consumption') {
                response = await consumptionLogAPI.queryConsumptionLogs(queryData);
            } else if (type === 'points') {
                response = await pointsLogAPI.queryPointsLogs(queryData);
            }

            if (response.data.code === 200) {
                const responseData = response.data.data;
                console.log('记录分页数据:', responseData);
                setRecords(responseData.records || []);
                setPagination(prev => ({
                    ...prev,
                    total: responseData.total || 0,
                    pages: responseData.pages || 0
                }));
            }
        } catch (error) {
            console.error('获取记录失败:', error);
        } finally {
            setLoading(false);
        }
    }, [member, type, pagination.current, pagination.size]); // 添加 pagination.size 作为依赖项

    useEffect(() => {
        if (visible && member && type) {
            fetchRecords();
        }
    }, [visible, member, type, fetchRecords]);

    // 分页处理
    const handlePageChange = (page) => {
        setPagination(prev => ({ ...prev, current: page }));
    };

    // 格式化金额
    const formatCurrency = (amount) => {
        return `¥${Number(amount || 0).toFixed(2)}`;
    };

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 获取积分变动类型显示名称
    const getPointsChangeTypeName = (changeType) => {
        const typeMap = {
            'earn_from_order': '下单获赠',
            'spend_on_order': '下单抵扣',
            'daily_checkin': '每日签到',
            'birthday_bonus': '生日奖励',
            'manual_adjustment': '手动调整',
            'system_reward': '系统奖励',
            'activity_bonus': '活动奖励'
        };
        return typeMap[changeType] || changeType;
    };

    const handleClose = () => {
        setRecords([]);
        setPagination({
            current: 1,
            size: 10,
            total: 0,
            pages: 0
        });
        onClose();
    };

    if (!visible || !member) return null;

    const modalTitle = type === 'consumption' ? '消费记录' : '积分记录';

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                {/* 背景遮罩 */}
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                    onClick={handleClose}
                ></div>

                {/* 模态框内容 */}
                <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                    {/* 头部 */}
                    <div className="bg-white px-6 py-4 border-b border-apple-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-semibold text-apple-gray-900">
                                    {modalTitle}
                                </h3>
                                <p className="text-sm text-apple-gray-500 mt-1">
                                    {member.nickname || '未设置'} - {member.phoneNumber || '未绑定手机'}
                                </p>
                            </div>
                            <button
                                type="button"
                                onClick={handleClose}
                                className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                            >
                                <i className="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>

                    {/* 记录列表 */}
                    <div className="bg-white max-h-96 overflow-y-auto">
                        {loading ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue"></div>
                                <span className="ml-2 text-apple-gray-600">加载中...</span>
                            </div>
                        ) : records.length > 0 ? (
                            <div className="divide-y divide-apple-gray-200">
                                {records.map((record, index) => (
                                    <div key={record.id || index} className="px-6 py-4 hover:bg-apple-gray-50">
                                        {type === 'consumption' ? (
                                            // 消费记录
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center">
                                                        <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                                            <i className="fas fa-minus text-red-600 text-sm"></i>
                                                        </div>
                                                        <div className="ml-3">
                                                            <div className="text-sm font-medium text-apple-gray-900">
                                                                订单消费
                                                            </div>
                                                            <div className="text-sm text-apple-gray-500">
                                                                订单号: {record.orderId || '-'}
                                                            </div>
                                                            <div className="text-xs text-apple-gray-400">
                                                                {formatDate(record.createdAt)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-sm font-medium text-red-600">
                                                        -{formatCurrency(record.amountConsumed)}
                                                    </div>
                                                    <div className="text-xs text-apple-gray-500">
                                                        余额: {formatCurrency(record.balanceBefore)} → {formatCurrency(record.balanceAfter)}
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            // 积分记录
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center">
                                                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                                                            record.pointsChange > 0 ? 'bg-green-100' : 'bg-red-100'
                                                        }`}>
                                                            <i className={`fas ${
                                                                record.pointsChange > 0 ? 'fa-plus text-green-600' : 'fa-minus text-red-600'
                                                            } text-sm`}></i>
                                                        </div>
                                                        <div className="ml-3">
                                                            <div className="text-sm font-medium text-apple-gray-900">
                                                                {getPointsChangeTypeName(record.changeType)}
                                                            </div>
                                                            {record.description && (
                                                                <div className="text-sm text-apple-gray-500">
                                                                    {record.description}
                                                                </div>
                                                            )}
                                                            {record.orderId && (
                                                                <div className="text-sm text-apple-gray-500">
                                                                    订单号: {record.orderId}
                                                                </div>
                                                            )}
                                                            <div className="text-xs text-apple-gray-400">
                                                                {formatDate(record.createdAt)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className={`text-sm font-medium ${
                                                        record.pointsChange > 0 ? 'text-green-600' : 'text-red-600'
                                                    }`}>
                                                        {record.pointsChange > 0 ? '+' : ''}{record.pointsChange}
                                                    </div>
                                                    <div className="text-xs text-apple-gray-500">
                                                        积分: {record.pointsBefore} → {record.pointsAfter}
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12">
                                <i className="fas fa-inbox text-4xl text-apple-gray-300 mb-4"></i>
                                <p className="text-apple-gray-500">暂无{modalTitle}数据</p>
                            </div>
                        )}
                    </div>

                    {/* 分页 - 始终显示分页信息 */}
                    {pagination.total > 0 && (
                        <div className="bg-white px-6 py-4 border-t border-apple-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-apple-gray-700">
                                    显示第 {(pagination.current - 1) * pagination.size + 1} 到{' '}
                                    {Math.min(pagination.current * pagination.size, pagination.total)} 条，
                                    共 {pagination.total} 条记录
                                </div>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => handlePageChange(pagination.current - 1)}
                                        disabled={pagination.current <= 1}
                                        className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        上一页
                                    </button>
                                    <span className="px-3 py-1 text-sm text-apple-gray-600">
                                        {pagination.current} / {Math.max(1, pagination.pages)}
                                    </span>
                                    <button
                                        onClick={() => handlePageChange(pagination.current + 1)}
                                        disabled={pagination.current >= Math.max(1, pagination.pages)}
                                        className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 底部按钮 */}
                    <div className="bg-apple-gray-50 px-6 py-4 flex justify-end">
                        <button
                            onClick={handleClose}
                            className="bg-white text-apple-gray-700 px-4 py-2 rounded-apple border border-apple-gray-300 hover:bg-apple-gray-50 transition-colors"
                        >
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RecordsModal;
