import React, { useState, useEffect } from 'react';
import { productAPI, categoryAPI } from '../services/api';

const ProductModal = ({ visible, onClose, onSuccess, product = null, type = 'create' }) => {
    const [formData, setFormData] = useState({
        name: '',
        categoryId: '',
        price: '',
        stockQuantity: '',
        alertQuantity: '',
        status: 'PUBLISHED',
        productCode: '',
        description: '',
        imageUrl: ''
    });
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (visible) {
            fetchCategories();
            if (product && type === 'edit') {
                setFormData({
                    id: product.id,
                    name: product.name || '',
                    categoryId: product.categoryId || '',
                    price: product.price || '',
                    stockQuantity: product.stockQuantity || '',
                    alertQuantity: product.alertQuantity || '',
                    status: product.status || 'PUBLISHED',
                    productCode: product.productCode || '',
                    description: product.description || '',
                    imageUrl: product.imageUrl || ''
                });
            } else {
                setFormData({
                    name: '',
                    categoryId: '',
                    price: '',
                    stockQuantity: '',
                    alertQuantity: '',
                    status: 'PUBLISHED',
                    productCode: '',
                    description: '',
                    imageUrl: ''
                });
            }
            setErrors({});
        }
    }, [visible, product, type]);

    const fetchCategories = async () => {
        try {
            const response = await categoryAPI.getTopLevelCategories();
            if (response.data.code === 200) {
                setCategories(response.data.data || []);
            }
        } catch (error) {
            console.error('获取分类列表失败:', error);
        }
    };

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.name.trim()) {
            newErrors.name = '商品名称不能为空';
        }
        
        if (!formData.categoryId) {
            newErrors.categoryId = '请选择商品分类';
        }
        
        if (!formData.price || parseFloat(formData.price) <= 0) {
            newErrors.price = '请输入有效的商品价格';
        }
        
        if (!formData.stockQuantity || parseInt(formData.stockQuantity) < 0) {
            newErrors.stockQuantity = '请输入有效的库存数量';
        }
        
        if (!formData.alertQuantity || parseInt(formData.alertQuantity) < 0) {
            newErrors.alertQuantity = '请输入有效的预警数量';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // 清除对应字段的错误
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            // 处理数据类型
            const submitData = {
                ...formData,
                categoryId: parseInt(formData.categoryId),
                price: parseFloat(formData.price),
                stockQuantity: parseInt(formData.stockQuantity),
                alertQuantity: parseInt(formData.alertQuantity)
            };

            let response;
            if (type === 'create') {
                response = await productAPI.createProduct(submitData);
            } else if (type === 'edit') {
                response = await productAPI.updateProduct(submitData);
            }

            if (response.data.code === 200) {
                onSuccess();
                onClose();
            } else {
                alert(response.data.msg || '操作失败');
            }
        } catch (error) {
            console.error('商品操作失败:', error);
            alert('操作失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    if (!visible) return null;

    const modalTitle = type === 'create' ? '新增商品' : type === 'edit' ? '编辑商品' : '商品详情';
    const isReadonly = type === 'view';

    return (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
                {/* 标题栏 */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 className="text-xl font-semibold text-apple-gray-900">{modalTitle}</h3>
                    <button
                        onClick={onClose}
                        className="p-2 text-apple-gray-400 hover:text-apple-gray-600 transition-colors rounded-apple hover:bg-apple-gray-100"
                    >
                        <i className="fas fa-times text-lg"></i>
                    </button>
                </div>

                {/* 表单内容 */}
                <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* 商品名称 */}
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    商品名称 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    placeholder="请输入商品名称"
                                    readOnly={isReadonly}
                                    className={`apple-input w-full px-4 py-3 rounded-apple ${
                                        errors.name ? 'border-red-500 focus:border-red-500' : ''
                                    } ${isReadonly ? 'bg-gray-50' : ''}`}
                                />
                                {errors.name && (
                                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                                )}
                            </div>

                            {/* 商品分类 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    商品分类 <span className="text-red-500">*</span>
                                </label>
                                <select
                                    name="categoryId"
                                    value={formData.categoryId}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    className={`apple-input w-full px-4 py-3 rounded-apple ${
                                        errors.categoryId ? 'border-red-500 focus:border-red-500' : ''
                                    } ${isReadonly ? 'bg-gray-50' : ''}`}
                                >
                                    <option value="">请选择分类</option>
                                    {categories.map(category => (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                                {errors.categoryId && (
                                    <p className="text-red-500 text-sm mt-1">{errors.categoryId}</p>
                                )}
                            </div>

                            {/* 商品价格 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    商品价格 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="number"
                                    name="price"
                                    value={formData.price}
                                    onChange={handleInputChange}
                                    placeholder="请输入价格"
                                    min="0"
                                    step="0.01"
                                    readOnly={isReadonly}
                                    className={`apple-input w-full px-4 py-3 rounded-apple ${
                                        errors.price ? 'border-red-500 focus:border-red-500' : ''
                                    } ${isReadonly ? 'bg-gray-50' : ''}`}
                                />
                                {errors.price && (
                                    <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                                )}
                            </div>

                            {/* 库存数量 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    库存数量 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="number"
                                    name="stockQuantity"
                                    value={formData.stockQuantity}
                                    onChange={handleInputChange}
                                    placeholder="请输入库存数量"
                                    min="0"
                                    readOnly={isReadonly}
                                    className={`apple-input w-full px-4 py-3 rounded-apple ${
                                        errors.stockQuantity ? 'border-red-500 focus:border-red-500' : ''
                                    } ${isReadonly ? 'bg-gray-50' : ''}`}
                                />
                                {errors.stockQuantity && (
                                    <p className="text-red-500 text-sm mt-1">{errors.stockQuantity}</p>
                                )}
                            </div>

                            {/* 预警数量 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    预警数量 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="number"
                                    name="alertQuantity"
                                    value={formData.alertQuantity}
                                    onChange={handleInputChange}
                                    placeholder="请输入预警数量"
                                    min="0"
                                    readOnly={isReadonly}
                                    className={`apple-input w-full px-4 py-3 rounded-apple ${
                                        errors.alertQuantity ? 'border-red-500 focus:border-red-500' : ''
                                    } ${isReadonly ? 'bg-gray-50' : ''}`}
                                />
                                {errors.alertQuantity && (
                                    <p className="text-red-500 text-sm mt-1">{errors.alertQuantity}</p>
                                )}
                            </div>
                        </div>

                        {/* 商品编码 */}
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                商品编码
                            </label>
                            <input
                                type="text"
                                name="productCode"
                                value={formData.productCode}
                                onChange={handleInputChange}
                                placeholder="请输入商品编码"
                                readOnly={isReadonly}
                                className={`apple-input w-full px-4 py-3 rounded-apple ${
                                    isReadonly ? 'bg-gray-50' : ''
                                }`}
                            />
                        </div>

                        {/* 商品描述 */}
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                商品描述
                            </label>
                            <textarea
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                placeholder="请输入商品描述"
                                rows="3"
                                readOnly={isReadonly}
                                className={`apple-input w-full px-4 py-3 rounded-apple resize-none ${
                                    isReadonly ? 'bg-gray-50' : ''
                                }`}
                            />
                        </div>

                        {/* 商品图片 */}
                        <div>
                            <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                商品图片
                            </label>
                            <input
                                type="url"
                                name="imageUrl"
                                value={formData.imageUrl}
                                onChange={handleInputChange}
                                placeholder="请输入图片URL"
                                readOnly={isReadonly}
                                className={`apple-input w-full px-4 py-3 rounded-apple ${
                                    isReadonly ? 'bg-gray-50' : ''
                                }`}
                            />
                        </div>

                        {/* 操作按钮 */}
                        {!isReadonly && (
                            <div className="flex space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="flex-1 px-4 py-3 text-apple-gray-700 bg-apple-gray-100 rounded-apple hover:bg-apple-gray-200 transition-colors"
                                >
                                    取消
                                </button>
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="flex-1 px-4 py-3 bg-apple-blue text-white rounded-apple hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {loading ? (
                                        <div className="flex items-center justify-center">
                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                            处理中...
                                        </div>
                                    ) : (
                                        type === 'create' ? '创建' : '保存'
                                    )}
                                </button>
                            </div>
                        )}
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ProductModal;
