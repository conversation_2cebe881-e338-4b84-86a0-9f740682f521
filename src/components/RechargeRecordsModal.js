import React, { useState, useEffect, useCallback } from 'react';
import { rechargeLogAPI } from '../services/api';
import { safeMemberId, safeProcessLargeIntegers } from '../utils/numberUtils';

const RechargeRecordsModal = ({ visible, member, onClose }) => {
    const [loading, setLoading] = useState(false);
    const [records, setRecords] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });

    // 搜索条件
    const [searchForm, setSearchForm] = useState({
        status: ''
    });

    // 充值状态选项
    const statusOptions = [
        { value: 'pending', label: '待处理', color: 'yellow' },
        { value: 'successful', label: '成功', color: 'green' },
        { value: 'failed', label: '失败', color: 'red' },
        { value: 'refunded', label: '退款', color: 'gray' }
    ];

    // 获取充值记录数据
    const fetchRecords = useCallback(async () => {
        if (!member) return;

        setLoading(true);
        try {
            const queryData = {
                current: pagination.current,
                size: pagination.size,
                memberId: safeMemberId(member.memberId),
                ...searchForm
            };

            const response = await rechargeLogAPI.queryRechargeLogs(queryData);

            if (response.data.code === 200) {
                const responseData = response.data.data;
                console.log('充值记录分页数据:', responseData);
                
                // 处理充值记录数据，确保大整数字段为字符串
                const processedRecords = safeProcessLargeIntegers(responseData.records || [], ['memberId', 'id', 'staffId']);
                
                setRecords(processedRecords);
                setPagination(prev => ({
                    ...prev,
                    total: responseData.total || 0,
                    pages: responseData.pages || 0
                }));
            }
        } catch (error) {
            console.error('获取充值记录失败:', error);
        } finally {
            setLoading(false);
        }
    }, [member, pagination.current, pagination.size, searchForm]);

    useEffect(() => {
        if (visible && member) {
            fetchRecords();
        }
    }, [visible, member, fetchRecords]);

    // 分页处理
    const handlePageChange = (page) => {
        setPagination(prev => ({ ...prev, current: page }));
    };

    // 搜索处理
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchRecords();
    };

    // 重置搜索
    const handleReset = () => {
        setSearchForm({ status: '' });
        setPagination(prev => ({ ...prev, current: 1 }));
        setTimeout(fetchRecords, 100);
    };

    // 格式化金额
    const formatCurrency = (amount) => {
        return `¥${Number(amount || 0).toFixed(2)}`;
    };

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
        const statusOption = statusOptions.find(option => option.value === status);
        return statusOption ? statusOption.color : 'gray';
    };

    // 获取支付方式显示名称
    const getPaymentMethodName = (method, methodName) => {
        return methodName || (method === 'cash' ? '现金支付' : method === 'offline_scan' ? '线下扫码' : method);
    };

    const handleClose = () => {
        setRecords([]);
        setPagination({
            current: 1,
            size: 10,
            total: 0,
            pages: 0
        });
        setSearchForm({ status: '' });
        onClose();
    };

    if (!visible || !member) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                {/* 背景遮罩 */}
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                    onClick={handleClose}
                ></div>

                {/* 模态框内容 */}
                <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
                    {/* 头部 */}
                    <div className="bg-white px-6 py-4 border-b border-apple-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-semibold text-apple-gray-900">
                                    充值记录
                                </h3>
                                <p className="text-sm text-apple-gray-500 mt-1">
                                    {member.nickname || '未设置'} - {member.phoneNumber || '未绑定手机'}
                                </p>
                            </div>
                            <button
                                type="button"
                                onClick={handleClose}
                                className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                            >
                                <i className="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>

                    {/* 搜索区域 */}
                    <div className="bg-apple-gray-50 px-6 py-4 border-b border-apple-gray-200">
                        <div className="flex items-center space-x-4">
                            <div className="flex-1">
                                <select
                                    value={searchForm.status}
                                    onChange={(e) => setSearchForm(prev => ({ ...prev, status: e.target.value }))}
                                    className="apple-input px-3 py-2 rounded-apple"
                                >
                                    <option value="">全部状态</option>
                                    {statusOptions.map(option => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <button
                                onClick={handleSearch}
                                className="apple-button px-4 py-2 rounded-apple"
                            >
                                <i className="fas fa-search mr-2"></i>
                                搜索
                            </button>
                            <button
                                onClick={handleReset}
                                className="bg-apple-gray-200 text-apple-gray-700 px-4 py-2 rounded-apple hover:bg-apple-gray-300 transition-colors"
                            >
                                重置
                            </button>
                        </div>
                        <div className="mt-2 text-sm text-apple-gray-600">
                            共找到 {pagination.total} 条充值记录
                        </div>
                    </div>

                    {/* 记录列表 */}
                    <div className="bg-white max-h-96 overflow-y-auto">
                        {loading ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue"></div>
                                <span className="ml-2 text-apple-gray-600">加载中...</span>
                            </div>
                        ) : records.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-apple-gray-50">
                                        <tr>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                订单号
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                金额
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                余额变化
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                支付方式
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                状态
                                            </th>
                                            <th className="px-4 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                                时间
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-apple-gray-200">
                                        {records.map((record) => (
                                            <tr key={record.id} className="hover:bg-apple-gray-50">
                                                <td className="px-4 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-apple-gray-900">
                                                        {record.rechargeOrderNumber}
                                                    </div>
                                                    {record.paymentTransactionId && (
                                                        <div className="text-xs text-apple-gray-500">
                                                            交易号: {record.paymentTransactionId}
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-4 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-green-600">
                                                        +{formatCurrency(record.amount)}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4 whitespace-nowrap">
                                                    <div className="text-xs text-apple-gray-500">
                                                        {formatCurrency(record.balanceBefore)} → {formatCurrency(record.balanceAfter)}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-apple-gray-900">
                                                        {getPaymentMethodName(record.paymentMethod, record.paymentMethodName)}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getStatusColor(record.status)}-100 text-${getStatusColor(record.status)}-800`}>
                                                        {record.statusName || record.status}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-4 whitespace-nowrap text-sm text-apple-gray-500">
                                                    {formatDate(record.createdAt)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12">
                                <i className="fas fa-receipt text-4xl text-apple-gray-300 mb-4"></i>
                                <p className="text-apple-gray-500">暂无充值记录</p>
                            </div>
                        )}
                    </div>

                    {/* 分页 - 始终显示分页信息 */}
                    {pagination.total > 0 && (
                        <div className="bg-white px-6 py-4 border-t border-apple-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-apple-gray-700">
                                    显示第 {(pagination.current - 1) * pagination.size + 1} 到{' '}
                                    {Math.min(pagination.current * pagination.size, pagination.total)} 条，
                                    共 {pagination.total} 条记录
                                </div>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => handlePageChange(pagination.current - 1)}
                                        disabled={pagination.current <= 1}
                                        className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        上一页
                                    </button>
                                    <span className="px-3 py-1 text-sm text-apple-gray-600">
                                        {pagination.current} / {Math.max(1, pagination.pages)}
                                    </span>
                                    <button
                                        onClick={() => handlePageChange(pagination.current + 1)}
                                        disabled={pagination.current >= Math.max(1, pagination.pages)}
                                        className="px-3 py-1 text-sm border border-apple-gray-300 rounded-apple hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        下一页
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 底部按钮 */}
                    <div className="bg-apple-gray-50 px-6 py-4 flex justify-end">
                        <button
                            onClick={handleClose}
                            className="bg-white text-apple-gray-700 px-4 py-2 rounded-apple border border-apple-gray-300 hover:bg-apple-gray-50 transition-colors"
                        >
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RechargeRecordsModal;
