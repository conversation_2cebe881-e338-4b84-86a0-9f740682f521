import React, { useState, useEffect } from 'react';
import { orderAPI } from '../services/api';

const OrderModal = ({ visible, onClose, orderId, onOrderUpdate }) => {
    const [order, setOrder] = useState(null);
    const [loading, setLoading] = useState(false);
    const [updating, setUpdating] = useState(false);

    // 订单状态选项
    const orderStatusOptions = [
        { value: 'PENDING_PAYMENT', label: '待支付' },
        { value: 'PROCESSING', label: '制作中' },
        { value: 'COMPLETED', label: '已完成' },
        { value: 'CANCELLED', label: '已取消' }
    ];

    // 支付状态选项
    const paymentStatusOptions = [
        { value: 'UNPAID', label: '未支付' },
        { value: 'PAID', label: '已支付' },
        { value: 'REFUNDED', label: '已退款' }
    ];

    useEffect(() => {
        if (visible && orderId) {
            fetchOrderDetail();
        }
    }, [visible, orderId]); // eslint-disable-line react-hooks/exhaustive-deps

    const fetchOrderDetail = async () => {
        setLoading(true);
        try {
            const response = await orderAPI.getOrder(orderId);
            if (response.data.code === 200) {
                setOrder(response.data.data);
            } else {
                alert('获取订单详情失败：' + response.data.msg);
            }
        } catch (error) {
            console.error('获取订单详情失败:', error);
            alert('获取订单详情失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handleStatusUpdate = async (newStatus) => {
        if (!order) return;

        setUpdating(true);
        try {
            const response = await orderAPI.updateOrderStatus({
                orderId: order.id,
                status: newStatus
            });
            
            if (response.data.code === 200) {
                setOrder({ ...order, status: newStatus });
                if (onOrderUpdate) {
                    onOrderUpdate();
                }
                alert('订单状态更新成功');
            } else {
                alert('更新失败：' + response.data.msg);
            }
        } catch (error) {
            console.error('更新订单状态失败:', error);
            alert('更新失败，请重试');
        } finally {
            setUpdating(false);
        }
    };

    const handlePaymentStatusUpdate = async (newPaymentStatus) => {
        if (!order) return;

        setUpdating(true);
        try {
            const response = await orderAPI.updatePaymentStatus({
                orderId: order.id,
                paymentStatus: newPaymentStatus
            });
            
            if (response.data.code === 200) {
                setOrder({ ...order, paymentStatus: newPaymentStatus });
                if (onOrderUpdate) {
                    onOrderUpdate();
                }
                alert('支付状态更新成功');
            } else {
                alert('更新失败：' + response.data.msg);
            }
        } catch (error) {
            console.error('更新支付状态失败:', error);
            alert('更新失败，请重试');
        } finally {
            setUpdating(false);
        }
    };

    const handleDeleteOrder = async () => {
        if (!order) return;

        if (!window.confirm(`确定要删除订单 ${order.orderNo} 吗？此操作不可恢复。`)) {
            return;
        }

        setUpdating(true);
        try {
            const response = await orderAPI.deleteOrder(order.id);
            if (response.data.code === 200) {
                if (onOrderUpdate) {
                    onOrderUpdate();
                }
                alert('订单删除成功');
                onClose();
            } else {
                alert('删除失败：' + response.data.msg);
            }
        } catch (error) {
            console.error('删除订单失败:', error);
            alert('删除失败，请重试');
        } finally {
            setUpdating(false);
        }
    };

    const handleClose = () => {
        setOrder(null);
        if (onClose) {
            onClose();
        }
    };

    if (!visible) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-apple-gray-900">订单详情</h3>
                    <button
                        onClick={handleClose}
                        className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                    >
                        <i className="fas fa-times text-xl"></i>
                    </button>
                </div>

                {loading ? (
                    <div className="flex items-center justify-center py-12">
                        <i className="fas fa-spinner fa-spin text-2xl text-apple-gray-400"></i>
                        <span className="ml-2 text-apple-gray-600">加载中...</span>
                    </div>
                ) : order ? (
                    <div className="space-y-6">
                        {/* 基本信息 */}
                        <div className="bg-apple-gray-50 rounded-lg p-4">
                            <h4 className="font-medium text-apple-gray-900 mb-3">基本信息</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-apple-gray-600">订单号:</span>
                                    <span className="ml-2 text-apple-gray-900 font-mono">{order.orderNo}</span>
                                </div>
                                <div>
                                    <span className="text-apple-gray-600">订单类型:</span>
                                    <span className="ml-2 text-apple-gray-900">{order.orderTypeDescription}</span>
                                </div>
                                <div>
                                    <span className="text-apple-gray-600">桌台:</span>
                                    <span className="ml-2 text-apple-gray-900">
                                        {order.tableNumber ? `${order.tableNumber}号桌` : '无'}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-apple-gray-600">手机号:</span>
                                    <span className="ml-2 text-apple-gray-900">
                                        {order.phoneNumber || '无'}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-apple-gray-600">创建时间:</span>
                                    <span className="ml-2 text-apple-gray-900">
                                        {new Date(order.createdAt).toLocaleString()}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-apple-gray-600">更新时间:</span>
                                    <span className="ml-2 text-apple-gray-900">
                                        {new Date(order.updatedAt).toLocaleString()}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {/* 状态信息 */}
                        <div className="bg-apple-gray-50 rounded-lg p-4">
                            <h4 className="font-medium text-apple-gray-900 mb-3">状态信息</h4>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm text-apple-gray-600 mb-1">订单状态</label>
                                    <select
                                        value={order.status}
                                        onChange={(e) => handleStatusUpdate(e.target.value)}
                                        disabled={updating}
                                        className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent disabled:opacity-50"
                                    >
                                        {orderStatusOptions.map(option => (
                                            <option key={option.value} value={option.value}>
                                                {option.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm text-apple-gray-600 mb-1">支付状态</label>
                                    <select
                                        value={order.paymentStatus}
                                        onChange={(e) => handlePaymentStatusUpdate(e.target.value)}
                                        disabled={updating}
                                        className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent disabled:opacity-50"
                                    >
                                        {paymentStatusOptions.map(option => (
                                            <option key={option.value} value={option.value}>
                                                {option.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* 金额信息 */}
                        <div className="bg-apple-gray-50 rounded-lg p-4">
                            <h4 className="font-medium text-apple-gray-900 mb-3">金额信息</h4>
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">原价:</span>
                                    <span className="text-apple-gray-900">¥{order.originalAmount}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-apple-gray-600">优惠:</span>
                                    <span className="text-red-600">-¥{order.discountAmount}</span>
                                </div>
                                <div className="flex justify-between font-medium text-base border-t pt-2">
                                    <span className="text-apple-gray-900">应付金额:</span>
                                    <span className="text-apple-blue">¥{order.payableAmount}</span>
                                </div>
                            </div>
                        </div>

                        {/* 商品列表 */}
                        {order.items && order.items.length > 0 && (
                            <div className="bg-apple-gray-50 rounded-lg p-4">
                                <h4 className="font-medium text-apple-gray-900 mb-3">商品列表</h4>
                                <div className="space-y-2">
                                    {order.items.map((item, index) => (
                                        <div key={index} className="flex justify-between items-center py-2 border-b border-apple-gray-200 last:border-b-0">
                                            <div>
                                                <span className="text-apple-gray-900">{item.productName}</span>
                                                <span className="text-apple-gray-500 ml-2">x{item.quantity}</span>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-apple-gray-900">¥{item.totalPrice}</div>
                                                <div className="text-xs text-apple-gray-500">单价: ¥{item.unitPrice}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* 优惠信息 */}
                        {order.discounts && order.discounts.length > 0 && (
                            <div className="bg-apple-gray-50 rounded-lg p-4">
                                <h4 className="font-medium text-apple-gray-900 mb-3">优惠信息</h4>
                                <div className="space-y-2">
                                    {order.discounts.map((discount, index) => (
                                        <div key={index} className="flex justify-between items-center">
                                            <div>
                                                <span className="text-apple-gray-900">{discount.description}</span>
                                                <span className="text-xs text-apple-gray-500 ml-2">
                                                    ({discount.discountTypeDescription})
                                                </span>
                                            </div>
                                            <span className="text-red-600">-¥{discount.amount}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* 备注 */}
                        {order.remark && (
                            <div className="bg-apple-gray-50 rounded-lg p-4">
                                <h4 className="font-medium text-apple-gray-900 mb-2">备注</h4>
                                <p className="text-apple-gray-700 text-sm">{order.remark}</p>
                            </div>
                        )}

                        {/* 操作按钮 */}
                        <div className="flex justify-end space-x-3 pt-4 border-t">
                            {/*<button*/}
                            {/*    onClick={handleDeleteOrder}*/}
                            {/*    disabled={updating}*/}
                            {/*    className="px-4 py-2 text-red-600 hover:text-red-800 transition-colors disabled:opacity-50"*/}
                            {/*>*/}
                            {/*    {updating ? '处理中...' : '删除订单'}*/}
                            {/*</button>*/}
                            <button
                                onClick={handleClose}
                                className="px-4 py-2 bg-apple-gray-500 text-white rounded-lg hover:bg-apple-gray-600 transition-colors"
                            >
                                关闭
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <p className="text-apple-gray-600">订单信息加载失败</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default OrderModal;
