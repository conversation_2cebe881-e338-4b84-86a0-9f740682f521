import React from 'react';

const ProductCard = ({ product, onEdit, onDelete, onStatusChange }) => {
    const getStatusInfo = (status, statusDescription) => {
        // 优先使用 statusDescription，如果没有则使用默认映射
        const displayText = statusDescription || (() => {
            switch (status) {
                case 'PUBLISHED':
                    return '已上架';
                case 'ARCHIVED':
                    return '已下架';
                case 'SOLDOUT':
                    return '售罄';
                default:
                    return '未知';
            }
        })();

        // 根据状态设置颜色和图标
        switch (status) {
            case 'PUBLISHED':
                return { text: displayText, color: 'bg-green-100 text-green-800', icon: 'fas fa-check-circle' };
            case 'ARCHIVED':
                return { text: displayText, color: 'bg-gray-100 text-gray-800', icon: 'fas fa-archive' };
            case 'SOLD_OUT':
            case 'SOLDOUT':
            case 'OUT_OF_STOCK':
                return { text: displayText, color: 'bg-red-100 text-red-800', icon: 'fas fa-times-circle' };
            default:
                return { text: displayText, color: 'bg-gray-100 text-gray-800', icon: 'fas fa-question-circle' };
        }
    };

    const statusInfo = getStatusInfo(product.status, product.statusDescription);
    const isLowStock = product.stockQuantity <= product.alertQuantity;

    const handleStatusChange = (newStatus) => {
        if (onStatusChange) {
            onStatusChange(product.id, newStatus);
        }
    };

    const handleEdit = () => {
        if (onEdit) {
            onEdit(product);
        }
    };

    const handleDelete = () => {
        if (onDelete && window.confirm(`确定要删除商品"${product.name}"吗？`)) {
            onDelete(product.id);
        }
    };

    return (
        <div className="apple-card rounded-2xl p-6 hover:shadow-apple-lg transition-all duration-300">
            {/* 商品图片 */}
            <div className="aspect-square rounded-xl mb-4 bg-apple-gray-100 flex items-center justify-center overflow-hidden">
                {product.imageUrl ? (
                    <img 
                        src={product.imageUrl} 
                        alt={product.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                        }}
                    />
                ) : null}
                <div className={`w-full h-full flex items-center justify-center ${product.imageUrl ? 'hidden' : 'flex'}`}>
                    <i className="fas fa-image text-3xl text-apple-gray-400"></i>
                </div>
            </div>

            {/* 商品信息 */}
            <div className="space-y-3">
                {/* 商品名称和状态 */}
                <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-apple-gray-900 text-lg leading-tight flex-1 mr-2">
                        {product.name}
                    </h3>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                        <i className={`${statusInfo.icon} mr-1`}></i>
                        {statusInfo.text}
                    </span>
                </div>

                {/* 商品编码 */}
                {product.productCode && (
                    <p className="text-sm text-apple-gray-500">
                        编码: {product.productCode}
                    </p>
                )}

                {/* 商品描述 */}
                {product.description && (
                    <p className="text-sm text-apple-gray-600 line-clamp-2">
                        {product.description}
                    </p>
                )}

                {/* 价格 */}
                <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-apple-blue font-sf-pro">
                        ¥{product.price}
                    </span>
                    <span className="text-sm text-apple-gray-500">
                        分类: {product.categoryName || '未分类'}
                    </span>
                </div>

                {/* 库存信息 */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <span className={`text-sm ${isLowStock ? 'text-red-600 font-medium' : 'text-apple-gray-600'}`}>
                            库存: {product.stockQuantity}
                            {isLowStock && (
                                <i className="fas fa-exclamation-triangle ml-1 text-red-500"></i>
                            )}
                        </span>
                        <span className="text-sm text-apple-gray-500">
                            预警: {product.alertQuantity}
                        </span>
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                    {/* 状态操作按钮 */}
                    <div className="flex items-center space-x-2">
                        {product.status === 'ARCHIVED' && (
                            <button
                                onClick={() => handleStatusChange('PUBLISHED')}
                                className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                                title="上架商品"
                            >
                                <i className="fas fa-arrow-up mr-1"></i>
                                上架
                            </button>
                        )}
                        {product.status === 'PUBLISHED' && (
                            <>
                                <button
                                    onClick={() => handleStatusChange('ARCHIVED')}
                                    className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                                    title="下架商品"
                                >
                                    <i className="fas fa-archive mr-1"></i>
                                    下架
                                </button>
                                <button
                                    onClick={() => handleStatusChange('SOLDOUT')}
                                    className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded-full hover:bg-red-200 transition-colors"
                                    title="标记售罄"
                                >
                                    <i className="fas fa-times-circle mr-1"></i>
                                    售罄
                                </button>
                            </>
                        )}
                        {product.status === 'SOLDOUT' && (
                            <>
                                <button
                                    onClick={() => handleStatusChange('PUBLISHED')}
                                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                                    title="取消售罄"
                                >
                                    <i className="fas fa-undo mr-1"></i>
                                    取消售罄
                                </button>
                                <button
                                    onClick={() => handleStatusChange('ARCHIVED')}
                                    className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                                    title="下架商品"
                                >
                                    <i className="fas fa-archive mr-1"></i>
                                    下架
                                </button>
                            </>
                        )}
                    </div>

                    {/* 编辑和删除按钮 */}
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleEdit}
                            className="p-2 text-apple-gray-400 hover:text-apple-blue transition-colors rounded-apple hover:bg-apple-gray-100"
                            title="编辑商品"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                        <button
                            onClick={handleDelete}
                            className="p-2 text-apple-gray-400 hover:text-red-500 transition-colors rounded-apple hover:bg-red-50"
                            title="删除商品"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProductCard;
