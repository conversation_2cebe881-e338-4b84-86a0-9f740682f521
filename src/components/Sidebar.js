import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ROUTES } from '../utils/config';

const Sidebar = ({ isOpen, onClose }) => {
    const { user, logout } = useAuth();
    const location = useLocation();

    const menuItems = [
        {
            path: ROUTES.DASHBOARD,
            icon: 'fas fa-home',
            label: '主页',
        },
        {
            path: ROUTES.TABLES,
            icon: 'fas fa-table',
            label: '桌台管理',
        },
        {
            path: ROUTES.POS,
            icon: 'fas fa-cash-register',
            label: '收银台',
        },
        {
            path: ROUTES.MEMBERS,
            icon: 'fas fa-users',
            label: '会员管理',
        },
        {
            path: ROUTES.INVENTORY,
            icon: 'fas fa-box',
            label: '库存管理',
        },
        {
            path: ROUTES.REPORTS,
            icon: 'fas fa-chart-bar',
            label: '数据报表',
        },
        {
            path: ROUTES.SETTINGS,
            icon: 'fas fa-cog',
            label: '系统设置',
        },
    ];

    const handleLogout = async () => {
        if (window.confirm('确定要退出登录吗？')) {
            await logout();
        }
    };

    const handleMenuClick = () => {
        // 在移动端点击菜单项后关闭侧边栏
        if (window.innerWidth <= 1024) {
            onClose();
        }
    };

    return (
        <>
            {/* 移动端遮罩层 */}
            {isOpen && (
                <div 
                    className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30 lg:hidden"
                    onClick={onClose}
                />
            )}

            {/* 侧边栏 */}
            <aside className={`
                fixed top-0 left-0 h-full w-80 bg-white/80 backdrop-blur-[20px] border-r border-gray-200/50 z-40
                transform transition-transform duration-300 ease-in-out
                ${isOpen ? 'translate-x-0' : '-translate-x-full'}
                lg:translate-x-0
            `}>
                {/* Logo 区域 */}
                <div className="p-6 border-b border-gray-200/50">
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                            <i className="fas fa-leaf text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 className="text-lg font-semibold text-apple-gray-900 font-sf-pro">茶馆收银</h1>
                            <p className="text-xs text-apple-gray-500">POS System</p>
                        </div>
                    </div>
                </div>

                {/* 导航菜单 */}
                <nav className="flex-1 py-6">
                    <div className="space-y-2 px-4">
                        {menuItems.map((item) => {
                            const isActive = location.pathname === item.path;
                            return (
                                <Link
                                    key={item.path}
                                    to={item.path}
                                    onClick={handleMenuClick}
                                    className={`
                                        flex items-center px-4 py-3 mx-2 rounded-apple text-sm font-medium
                                        transition-all duration-200 ease-in-out
                                        ${isActive 
                                            ? 'bg-apple-blue text-white shadow-md' 
                                            : 'text-apple-gray-700 hover:bg-apple-blue/8 hover:text-apple-blue'
                                        }
                                    `}
                                >
                                    <i className={`${item.icon} w-5 mr-3 text-base`}></i>
                                    <span>{item.label}</span>
                                </Link>
                            );
                        })}
                    </div>
                </nav>

                {/* 用户信息区域 */}
                <div className="p-6 border-t border-gray-200/50">
                    <div className="flex items-center space-x-3 mb-4">
                        <div className="w-10 h-10 bg-apple-blue rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">
                                {user?.fullName?.charAt(0) || user?.username?.charAt(0) || 'A'}
                            </span>
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-apple-gray-900 truncate">
                                {user?.fullName || user?.username || '管理员'}
                            </p>
                            <p className="text-xs text-apple-gray-500">在线</p>
                        </div>
                    </div>
                    <button 
                        onClick={handleLogout}
                        className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-apple-gray-700 bg-apple-gray-100 rounded-apple hover:bg-apple-gray-200 transition-colors"
                    >
                        <i className="fas fa-sign-out-alt mr-2"></i>
                        退出登录
                    </button>
                </div>
            </aside>
        </>
    );
};

export default Sidebar;
