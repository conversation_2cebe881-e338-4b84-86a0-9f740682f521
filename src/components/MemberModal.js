import React, { useState, useEffect } from 'react';
import { memberAPI } from '../services/api';
import { safeMemberId } from '../utils/numberUtils';

const MemberModal = ({ visible, type, data, membershipLevels, onClose, onSuccess }) => {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        nickname: '',
        phoneNumber: '',
        wxOpenid: '',
        wxUnionid: '',
        avatarUrl: '',
        membershipLevelId: 1,
        balance: 0,
        points: 0
    });

    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (visible) {
            if (type === 'edit' && data) {
                setFormData({
                    id: data.id,
                    memberId: safeMemberId(data.memberId), // 安全处理 memberId
                    nickname: data.nickname || '',
                    phoneNumber: data.phoneNumber || '',
                    wxOpenid: data.wxOpenid || '',
                    wxUnionid: data.wxUnionid || '',
                    avatarUrl: data.avatarUrl || '',
                    membershipLevelId: data.membershipLevelId || 1,
                    balance: data.balance || 0,
                    points: data.points || 0
                });
            } else {
                setFormData({
                    nickname: '',
                    phoneNumber: '',
                    wxOpenid: '',
                    wxUnionid: '',
                    avatarUrl: '',
                    membershipLevelId: 1,
                    balance: 0,
                    points: 0
                });
            }
            setErrors({});
        }
    }, [visible, type, data]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // 清除对应字段的错误
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.nickname.trim()) {
            newErrors.nickname = '请输入会员昵称';
        }

        if (!formData.phoneNumber.trim()) {
            newErrors.phoneNumber = '请输入手机号';
        } else if (!/^1[3-9]\d{9}$/.test(formData.phoneNumber)) {
            newErrors.phoneNumber = '请输入正确的手机号格式';
        }

        if (formData.balance < 0) {
            newErrors.balance = '余额不能为负数';
        }

        if (formData.points < 0) {
            newErrors.points = '积分不能为负数';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            let response;
            if (type === 'create') {
                response = await memberAPI.createMember(formData);
            } else if (type === 'edit') {
                response = await memberAPI.updateMember(formData);
            }

            if (response.data.code === 200) {
                onSuccess();
            } else {
                alert(response.data.msg || '操作失败');
            }
        } catch (error) {
            console.error('会员操作失败:', error);
            alert('操作失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    if (!visible) return null;

    const modalTitle = type === 'create' ? '新增会员' : type === 'edit' ? '编辑会员' : '会员详情';
    const isReadonly = type === 'view';

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                {/* 背景遮罩 */}
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                    onClick={onClose}
                ></div>

                {/* 模态框内容 */}
                <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <form onSubmit={handleSubmit}>
                        {/* 头部 */}
                        <div className="bg-white px-6 py-4 border-b border-apple-gray-200">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-apple-gray-900">
                                    {modalTitle}
                                </h3>
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="text-apple-gray-400 hover:text-apple-gray-600 transition-colors"
                                >
                                    <i className="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        {/* 表单内容 */}
                        <div className="bg-white px-6 py-4 space-y-4">
                            {/* 会员昵称 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    会员昵称 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    name="nickname"
                                    value={formData.nickname}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    placeholder="请输入会员昵称"
                                    className={`apple-input w-full px-3 py-2 rounded-apple ${errors.nickname ? 'border-red-300' : ''}`}
                                />
                                {errors.nickname && (
                                    <p className="mt-1 text-sm text-red-600">{errors.nickname}</p>
                                )}
                            </div>

                            {/* 手机号 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    手机号 <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="tel"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    placeholder="请输入手机号"
                                    className={`apple-input w-full px-3 py-2 rounded-apple ${errors.phoneNumber ? 'border-red-300' : ''}`}
                                />
                                {errors.phoneNumber && (
                                    <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
                                )}
                            </div>

                            {/* 会员等级 */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    会员等级
                                </label>
                                <select
                                    name="membershipLevelId"
                                    value={formData.membershipLevelId}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    className="apple-input w-full px-3 py-2 rounded-apple"
                                >
                                    {membershipLevels.map(level => (
                                        <option key={level.id} value={level.id}>
                                            {level.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* 微信OpenID */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    微信OpenID
                                </label>
                                <input
                                    type="text"
                                    name="wxOpenid"
                                    value={formData.wxOpenid}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    placeholder="请输入微信OpenID"
                                    className="apple-input w-full px-3 py-2 rounded-apple"
                                />
                            </div>

                            {/* 微信UnionID */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    微信UnionID
                                </label>
                                <input
                                    type="text"
                                    name="wxUnionid"
                                    value={formData.wxUnionid}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    placeholder="请输入微信UnionID"
                                    className="apple-input w-full px-3 py-2 rounded-apple"
                                />
                            </div>

                            {/* 头像URL */}
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                    头像URL
                                </label>
                                <input
                                    type="url"
                                    name="avatarUrl"
                                    value={formData.avatarUrl}
                                    onChange={handleInputChange}
                                    disabled={isReadonly}
                                    placeholder="请输入头像URL"
                                    className="apple-input w-full px-3 py-2 rounded-apple"
                                />
                            </div>

                            {/* 余额和积分 */}
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                        初始余额
                                    </label>
                                    <input
                                        type="number"
                                        name="balance"
                                        value={formData.balance}
                                        onChange={handleInputChange}
                                        disabled={isReadonly}
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        className={`apple-input w-full px-3 py-2 rounded-apple ${errors.balance ? 'border-red-300' : ''}`}
                                    />
                                    {errors.balance && (
                                        <p className="mt-1 text-sm text-red-600">{errors.balance}</p>
                                    )}
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                                        初始积分
                                    </label>
                                    <input
                                        type="number"
                                        name="points"
                                        value={formData.points}
                                        onChange={handleInputChange}
                                        disabled={isReadonly}
                                        min="0"
                                        placeholder="0"
                                        className={`apple-input w-full px-3 py-2 rounded-apple ${errors.points ? 'border-red-300' : ''}`}
                                    />
                                    {errors.points && (
                                        <p className="mt-1 text-sm text-red-600">{errors.points}</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* 底部按钮 */}
                        {!isReadonly && (
                            <div className="bg-apple-gray-50 px-6 py-4 flex justify-end space-x-3">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="bg-white text-apple-gray-700 px-4 py-2 rounded-apple border border-apple-gray-300 hover:bg-apple-gray-50 transition-colors"
                                >
                                    取消
                                </button>
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="apple-button px-4 py-2 rounded-apple disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {loading ? (
                                        <>
                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                            {type === 'create' ? '创建中...' : '保存中...'}
                                        </>
                                    ) : (
                                        type === 'create' ? '创建会员' : '保存修改'
                                    )}
                                </button>
                            </div>
                        )}
                    </form>
                </div>
            </div>
        </div>
    );
};

export default MemberModal;
