import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProductModal from '../components/ProductModal';
import { productAPI, categoryAPI, fileAPI } from '../services/api';

// Mock API calls
jest.mock('../services/api', () => ({
    productAPI: {
        createProduct: jest.fn(),
        updateProduct: jest.fn(),
    },
    categoryAPI: {
        getTopLevelCategories: jest.fn(),
    },
    fileAPI: {
        uploadFile: jest.fn(),
    },
}));

describe('ProductModal', () => {
    const mockOnClose = jest.fn();
    const mockOnSuccess = jest.fn();
    
    const mockCategories = [
        { id: 1, name: '饮品' },
        { id: 2, name: '小食' },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        categoryAPI.getTopLevelCategories.mockResolvedValue({
            data: { code: 200, data: mockCategories }
        });
    });

    test('renders create product modal correctly', async () => {
        render(
            <ProductModal
                visible={true}
                onClose={mockOnClose}
                onSuccess={mockOnSuccess}
                type="create"
            />
        );

        expect(screen.getByText('新增商品')).toBeInTheDocument();
        expect(screen.getByLabelText(/商品名称/)).toBeInTheDocument();
        expect(screen.getByLabelText(/商品图片/)).toBeInTheDocument();
        expect(screen.getByText('点击选择图片或拖拽图片到此处')).toBeInTheDocument();
    });

    test('handles file upload correctly', async () => {
        const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        
        fileAPI.uploadFile.mockResolvedValue({
            data: {
                code: 200,
                data: {
                    success: true,
                    fileUrl: 'http://example.com/test.jpg'
                }
            }
        });

        productAPI.createProduct.mockResolvedValue({
            data: { code: 200 }
        });

        render(
            <ProductModal
                visible={true}
                onClose={mockOnClose}
                onSuccess={mockOnSuccess}
                type="create"
            />
        );

        // 等待组件加载完成
        await waitFor(() => {
            expect(screen.getByText('新增商品')).toBeInTheDocument();
        });

        // 模拟文件选择
        const fileInput = screen.getByLabelText(/商品图片/).querySelector('input[type="file"]');
        fireEvent.change(fileInput, { target: { files: [mockFile] } });

        // 填写必填字段
        fireEvent.change(screen.getByLabelText(/商品名称/), { target: { value: '测试商品' } });
        fireEvent.change(screen.getByLabelText(/商品价格/), { target: { value: '10.00' } });
        fireEvent.change(screen.getByLabelText(/库存数量/), { target: { value: '100' } });
        fireEvent.change(screen.getByLabelText(/预警数量/), { target: { value: '10' } });

        // 等待分类加载
        await waitFor(() => {
            const categorySelect = screen.getByLabelText(/商品分类/);
            fireEvent.change(categorySelect, { target: { value: '1' } });
        });

        // 提交表单
        const submitButton = screen.getByText('创建');
        fireEvent.click(submitButton);

        // 验证文件上传被调用
        await waitFor(() => {
            expect(fileAPI.uploadFile).toHaveBeenCalledWith(mockFile);
        });

        // 验证商品创建被调用，且包含正确的图片URL
        await waitFor(() => {
            expect(productAPI.createProduct).toHaveBeenCalledWith(
                expect.objectContaining({
                    imageUrl: 'http://example.com/test.jpg'
                })
            );
        });
    });

    test('validates file type and size', () => {
        render(
            <ProductModal
                visible={true}
                onClose={mockOnClose}
                onSuccess={mockOnSuccess}
                type="create"
            />
        );

        // 测试无效文件类型
        const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
        const fileInput = screen.getByLabelText(/商品图片/).querySelector('input[type="file"]');
        
        // Mock alert
        window.alert = jest.fn();
        
        fireEvent.change(fileInput, { target: { files: [invalidFile] } });
        
        expect(window.alert).toHaveBeenCalledWith('请选择有效的图片文件（JPG、PNG、GIF）');
    });

    test('shows image preview when file is selected', async () => {
        const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        
        render(
            <ProductModal
                visible={true}
                onClose={mockOnClose}
                onSuccess={mockOnSuccess}
                type="create"
            />
        );

        const fileInput = screen.getByLabelText(/商品图片/).querySelector('input[type="file"]');
        
        // Mock FileReader
        const mockFileReader = {
            readAsDataURL: jest.fn(),
            onload: null,
            result: 'data:image/jpeg;base64,test'
        };
        
        global.FileReader = jest.fn(() => mockFileReader);
        
        fireEvent.change(fileInput, { target: { files: [mockFile] } });
        
        // 模拟FileReader onload事件
        mockFileReader.onload({ target: { result: 'data:image/jpeg;base64,test' } });
        
        await waitFor(() => {
            const previewImage = screen.getByAltText('预览');
            expect(previewImage).toBeInTheDocument();
            expect(previewImage).toHaveAttribute('src', 'data:image/jpeg;base64,test');
        });
    });
});
