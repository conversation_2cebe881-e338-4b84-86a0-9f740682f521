@tailwind base;
@tailwind components;
@tailwind utilities;

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&family=SF+Pro+Text:wght@300;400;500;600&display=swap');

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f7;
}

code {
  font-family: 'SF Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* Apple风格的全局样式 */
.apple-card {
  @apply bg-white/80 backdrop-blur-[20px] border border-white/20 shadow-apple;
}

.apple-button {
  @apply bg-apple-blue text-white border-none transition-all duration-300 font-medium;
}

.apple-button:hover {
  @apply bg-blue-600 transform -translate-y-0.5 shadow-lg;
}

.apple-button:active {
  @apply transform translate-y-0 shadow-md;
}

.apple-input {
  @apply bg-white/90 border border-gray-200 transition-all duration-300;
}

.apple-input:focus {
  @apply border-apple-blue ring-4 ring-apple-blue/10 outline-none;
}

/* 自定义动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 字体类 */
.font-sf-pro {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
