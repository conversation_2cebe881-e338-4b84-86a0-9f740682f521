import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
    const { login, isAuthenticated, error, clearError } = useAuth();
    const location = useLocation();
    
    const [formData, setFormData] = useState({
        username: 'admin',
        password: '123456',
    });
    const [rememberMe, setRememberMe] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // 清除错误信息
    useEffect(() => {
        if (error) {
            const timer = setTimeout(() => {
                clearError();
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [error, clearError]);

    // 如果已经登录，重定向到目标页面或主页
    if (isAuthenticated) {
        const from = location.state?.from?.pathname || '/dashboard';
        return <Navigate to={from} replace />;
    }

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            const result = await login(formData, rememberMe);
            if (result.success) {
                // 登录成功，AuthContext 会处理重定向
                console.log('登录成功');
            }
        } catch (error) {
            console.error('登录失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
                <div className="absolute top-3/4 right-1/4 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
                <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{animationDelay: '4s'}}></div>
            </div>

            {/* 登录卡片 */}
            <div className="w-full max-w-md mx-auto p-6 relative z-10">
                <div className="bg-white/90 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-white/20">
                    {/* Logo 和标题 */}
                    <div className="text-center mb-8">
                        <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-float">
                            <i className="fas fa-leaf text-white text-2xl"></i>
                        </div>
                        <h1 className="text-3xl font-semibold text-gray-900 mb-2">茶馆收银系统</h1>
                        <p className="text-gray-600 text-base">Tea House POS System</p>
                    </div>

                    {/* 登录表单 */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* 用户名输入框 */}
                        <div className="space-y-2">
                            <label htmlFor="username" className="block text-sm font-medium text-gray-900">
                                用户名
                            </label>
                            <div className="relative">
                                <input
                                    type="text"
                                    id="username"
                                    name="username"
                                    value={formData.username}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-3 rounded-xl text-base border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                                    placeholder="请输入用户名"
                                    required
                                />
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <i className="fas fa-user text-gray-400 text-sm"></i>
                                </div>
                            </div>
                        </div>

                        {/* 密码输入框 */}
                        <div className="space-y-2">
                            <label htmlFor="password" className="block text-sm font-medium text-gray-900">
                                密码
                            </label>
                            <div className="relative">
                                <input
                                    type={showPassword ? 'text' : 'password'}
                                    id="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-3 pr-12 rounded-xl text-base border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                                    placeholder="请输入密码"
                                    required
                                />
                                <button
                                    type="button"
                                    onClick={togglePasswordVisibility}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'} text-sm`}></i>
                                </button>
                            </div>
                        </div>

                        {/* 记住登录 */}
                        <div className="flex items-center justify-between">
                            <label className="flex items-center cursor-pointer">
                                <input 
                                    type="checkbox" 
                                    checked={rememberMe}
                                    onChange={(e) => setRememberMe(e.target.checked)}
                                    className="w-5 h-5 text-apple-blue border-apple-gray-300 rounded focus:ring-apple-blue focus:ring-2"
                                />
                                <span className="ml-2 text-sm text-gray-700">记住登录</span>
                            </label>
                            <button className="text-sm text-blue-600 hover:text-blue-800 transition-colors">忘记密码？</button>
                        </div>

                        {/* 登录按钮 */}
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full py-3 px-4 rounded-xl text-base font-medium flex items-center justify-center bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                        >
                            {isLoading ? (
                                <>
                                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                                    登录中...
                                </>
                            ) : (
                                '登录'
                            )}
                        </button>
                    </form>

                    {/* 错误提示 */}
                    {error && (
                        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                            <div className="flex items-center">
                                <i className="fas fa-exclamation-circle text-red-500 mr-2"></i>
                                <span className="text-red-700 text-sm">{error}</span>
                            </div>
                        </div>
                    )}

                    {/* 分割线 */}
                    <div className="relative my-8">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-200"></div>
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-4 bg-white text-gray-500">或</span>
                        </div>
                    </div>

                    {/* 其他登录方式 */}
                    <div className="space-y-3">
                        <button className="w-full py-3 px-4 border border-gray-200 rounded-xl text-gray-700 font-medium hover:bg-gray-50 transition-colors flex items-center justify-center">
                            <i className="fab fa-apple mr-3 text-lg"></i>
                            使用 Apple ID 登录
                        </button>
                        <button className="w-full py-3 px-4 border border-gray-200 rounded-xl text-gray-700 font-medium hover:bg-gray-50 transition-colors flex items-center justify-center">
                            <i className="fab fa-wechat mr-3 text-lg text-green-500"></i>
                            使用微信登录
                        </button>
                    </div>

                    {/* 版权信息 */}
                    <div className="text-center mt-8 pt-6 border-t border-gray-100">
                        <p className="text-xs text-gray-500">© 2024 茶馆收银系统. 保留所有权利.</p>
                        <p className="text-xs text-gray-400 mt-1">版本 1.0.0</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;
