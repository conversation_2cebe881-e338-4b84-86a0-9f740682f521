import React, { useState, useEffect, useCallback } from 'react';
import Layout from '../components/Layout';
import ProductModal from '../components/ProductModal';
import CategoryModal from '../components/CategoryModal';
import ProductCard from '../components/ProductCard';
import { productAPI, categoryAPI } from '../services/api';

const Inventory = () => {
    const [activeTab, setActiveTab] = useState('products');
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'table'
    
    // 商品相关状态
    const [products, setProducts] = useState([]);
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [productModalVisible, setProductModalVisible] = useState(false);
    const [productModalType, setProductModalType] = useState('create');
    const [selectedProduct, setSelectedProduct] = useState(null);

    // 分页相关状态
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });
    
    // 分类相关状态
    const [categories, setCategories] = useState([]);
    const [categoryModalVisible, setCategoryModalVisible] = useState(false);
    const [categoryModalType, setCategoryModalType] = useState('create');
    const [selectedCategory2, setSelectedCategory2] = useState(null);
    
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (activeTab === 'products') {
            fetchProducts();
            fetchCategories();
        } else if (activeTab === 'categories') {
            fetchCategories();
        }
    }, [activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (activeTab === 'products') {
            // 重置到第一页
            if (pagination.current !== 1) {
                setPagination(prev => ({ ...prev, current: 1 }));
            } else {
                fetchProducts();
            }
        }
    }, [selectedCategory, searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (activeTab === 'products') {
            fetchProducts();
        }
    }, [pagination.current, pagination.size]); // eslint-disable-line react-hooks/exhaustive-deps

    const fetchProducts = async () => {
        setLoading(true);
        try {
            const queryData = {
                current: pagination.current,
                size: pagination.size,
                name: searchTerm || undefined,
                categoryId: selectedCategory !== 'all' ? parseInt(selectedCategory) : undefined
            };

            const response = await productAPI.queryProducts(queryData);
            if (response.data.code === 200) {
                const responseData = response.data.data;
                setProducts(responseData.records || []);
                setFilteredProducts(responseData.records || []);
                setPagination(prev => ({
                    ...prev,
                    total: responseData.total || 0,
                    pages: responseData.pages || 0
                }));
            }
        } catch (error) {
            console.error('获取商品列表失败:', error);
            // 如果分页查询失败，尝试使用原来的接口
            try {
                const response = await productAPI.getProducts();
                if (response.data.code === 200) {
                    const allProducts = response.data.data || [];
                    setProducts(allProducts);
                    filterProductsLocally(allProducts);
                }
            } catch (fallbackError) {
                console.error('备用接口也失败:', fallbackError);
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            const response = await categoryAPI.getTopLevelCategories();
            if (response.data.code === 200) {
                setCategories(response.data.data || []);
            }
        } catch (error) {
            console.error('获取分类列表失败:', error);
        }
    };

    const filterProductsLocally = (productList) => {
        let filtered = productList;

        // 按分类筛选
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(product => product.categoryId === parseInt(selectedCategory));
        }

        // 按搜索词筛选
        if (searchTerm) {
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (product.productCode && product.productCode.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        setFilteredProducts(filtered);
    };

    // 处理分页变化
    const handlePageChange = (page) => {
        setPagination(prev => ({
            ...prev,
            current: page
        }));
    };

    // 处理页面大小变化
    const handlePageSizeChange = (size) => {
        setPagination(prev => ({
            ...prev,
            current: 1,
            size: size
        }));
    };

    // 防抖搜索
    const debouncedSearch = useCallback(
        debounce((term) => {
            setSearchTerm(term);
        }, 500),
        [] // eslint-disable-line react-hooks/exhaustive-deps
    );

    // 处理搜索输入
    const handleSearchChange = (e) => {
        const value = e.target.value;
        debouncedSearch(value);
    };

    // 简单的防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 渲染页码按钮
    const renderPageNumbers = () => {
        const pageNumbers = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, pagination.current - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(pagination.pages, startPage + maxVisiblePages - 1);

        // 调整起始页，确保显示足够的页码
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.push(
                <button
                    key={i}
                    onClick={() => handlePageChange(i)}
                    className={`px-3 py-1 text-sm rounded-apple border transition-colors ${
                        i === pagination.current
                            ? 'bg-apple-blue text-white border-apple-blue'
                            : 'border-gray-300 hover:bg-apple-gray-50'
                    }`}
                >
                    {i}
                </button>
            );
        }

        return pageNumbers;
    };

    // 商品操作
    const handleCreateProduct = () => {
        setSelectedProduct(null);
        setProductModalType('create');
        setProductModalVisible(true);
    };

    const handleEditProduct = (product) => {
        setSelectedProduct(product);
        setProductModalType('edit');
        setProductModalVisible(true);
    };

    const handleDeleteProduct = async (productId) => {
        try {
            const response = await productAPI.deleteProduct(productId);
            if (response.data.code === 200) {
                fetchProducts();
            } else {
                alert(response.data.msg || '删除失败');
            }
        } catch (error) {
            console.error('删除商品失败:', error);
            alert('删除失败，请重试');
        }
    };

    const handleProductStatusChange = async (productId, newStatus) => {
        try {
            let response;
            switch (newStatus) {
                case 'PUBLISHED':
                    response = await productAPI.publishProduct(productId);
                    break;
                case 'ARCHIVED':
                    response = await productAPI.archiveProduct(productId);
                    break;
                case 'SOLDOUT':
                    response = await productAPI.toggleSoldOut(productId);
                    break;
                default:
                    return;
            }

            if (response.data.code === 200) {
                fetchProducts();
            } else {
                alert(response.data.msg || '操作失败');
            }
        } catch (error) {
            console.error('状态更新失败:', error);
            alert('操作失败，请重试');
        }
    };

    // 分类操作
    const handleCreateCategory = () => {
        setSelectedCategory2(null);
        setCategoryModalType('create');
        setCategoryModalVisible(true);
    };

    const handleEditCategory = (category) => {
        setSelectedCategory2(category);
        setCategoryModalType('edit');
        setCategoryModalVisible(true);
    };

    const handleDeleteCategory = async (categoryId) => {
        if (window.confirm('确定要删除此分类吗？删除后该分类下的商品将变为未分类状态。')) {
            try {
                const response = await categoryAPI.deleteCategory(categoryId);
                if (response.data.code === 200) {
                    fetchCategories();
                    fetchProducts(); // 重新获取商品以更新分类信息
                } else {
                    alert(response.data.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除分类失败:', error);
                alert('删除失败，请重试');
            }
        }
    };

    const renderProductsTab = () => (
        <div className="space-y-6">
            {/* 工具栏 */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div className="flex items-center space-x-4">
                    {/* 搜索框 */}
                    <div className="relative">
                        <input
                            type="text"
                            placeholder="搜索商品名称或编码..."
                            onChange={handleSearchChange}
                            className="apple-input pl-10 pr-4 py-2 w-64 rounded-apple"
                        />
                        <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-apple-gray-400"></i>
                    </div>
                    
                    {/* 分类筛选 */}
                    <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="apple-input px-4 py-2 rounded-apple"
                    >
                        <option value="all">全部分类</option>
                        {categories.map(category => (
                            <option key={category.id} value={category.id}>
                                {category.name}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="flex items-center space-x-3">
                    {/* 视图切换 */}
                    <div className="flex items-center bg-apple-gray-100 rounded-apple p-1">
                        <button
                            onClick={() => setViewMode('grid')}
                            className={`px-3 py-1 rounded-apple text-sm transition-colors ${
                                viewMode === 'grid' 
                                    ? 'bg-white text-apple-gray-900 shadow-sm' 
                                    : 'text-apple-gray-600 hover:text-apple-gray-900'
                            }`}
                        >
                            <i className="fas fa-th mr-1"></i>
                            网格
                        </button>
                        <button
                            onClick={() => setViewMode('table')}
                            className={`px-3 py-1 rounded-apple text-sm transition-colors ${
                                viewMode === 'table' 
                                    ? 'bg-white text-apple-gray-900 shadow-sm' 
                                    : 'text-apple-gray-600 hover:text-apple-gray-900'
                            }`}
                        >
                            <i className="fas fa-list mr-1"></i>
                            列表
                        </button>
                    </div>
                    
                    {/* 新增商品按钮 */}
                    <button
                        onClick={handleCreateProduct}
                        className="apple-button bg-apple-blue text-white px-4 py-2 rounded-apple hover:bg-blue-600 transition-colors"
                    >
                        <i className="fas fa-plus mr-2"></i>
                        新增商品
                    </button>
                </div>
            </div>

            {/* 商品列表 */}
            {loading ? (
                <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                        <i className="fas fa-spinner fa-spin text-3xl text-apple-gray-400 mb-4"></i>
                        <p className="text-apple-gray-600">加载中...</p>
                    </div>
                </div>
            ) : filteredProducts.length === 0 ? (
                <div className="text-center py-12">
                    <i className="fas fa-box-open text-6xl text-apple-gray-300 mb-4"></i>
                    <h3 className="text-xl font-semibold text-apple-gray-900 mb-2">暂无商品</h3>
                    <p className="text-apple-gray-600 mb-4">
                        {searchTerm || selectedCategory !== 'all' ? '没有找到符合条件的商品' : '还没有添加任何商品'}
                    </p>
                    <button
                        onClick={handleCreateProduct}
                        className="apple-button bg-apple-blue text-white px-6 py-3 rounded-apple hover:bg-blue-600 transition-colors"
                    >
                        <i className="fas fa-plus mr-2"></i>
                        添加第一个商品
                    </button>
                </div>
            ) : viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredProducts.map(product => (
                        <ProductCard
                            key={product.id}
                            product={product}
                            onEdit={handleEditProduct}
                            onDelete={handleDeleteProduct}
                            onStatusChange={handleProductStatusChange}
                        />
                    ))}
                </div>
            ) : (
                <div className="apple-card rounded-2xl overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-apple-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">商品</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">分类</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">价格</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">库存</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">状态</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredProducts.map(product => (
                                    <tr key={product.id} className="hover:bg-apple-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0 h-10 w-10">
                                                    {product.imageUrl ? (
                                                        <img className="h-10 w-10 rounded-lg object-cover" src={product.imageUrl} alt={product.name} />
                                                    ) : (
                                                        <div className="h-10 w-10 rounded-lg bg-apple-gray-100 flex items-center justify-center">
                                                            <i className="fas fa-image text-apple-gray-400"></i>
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-apple-gray-900">{product.name}</div>
                                                    {product.productCode && (
                                                        <div className="text-sm text-apple-gray-500">{product.productCode}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                            {product.categoryName || '未分类'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-apple-blue">
                                            ¥{product.price}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                            <span className={product.stockQuantity <= product.alertQuantity ? 'text-red-600 font-medium' : ''}>
                                                {product.stockQuantity}
                                                {product.stockQuantity <= product.alertQuantity && (
                                                    <i className="fas fa-exclamation-triangle ml-1 text-red-500"></i>
                                                )}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                product.status === 'PUBLISHED' ? 'bg-green-100 text-green-800' :
                                                product.status === 'ARCHIVED' ? 'bg-gray-100 text-gray-800' :
                                                'bg-red-100 text-red-800'
                                            }`}>
                                                {product.statusDescription || (
                                                    product.status === 'PUBLISHED' ? '已上架' :
                                                    product.status === 'ARCHIVED' ? '已下架' : '售罄'
                                                )}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={() => handleEditProduct(product)}
                                                    className="text-apple-blue hover:text-blue-600"
                                                >
                                                    编辑
                                                </button>
                                                <button
                                                    onClick={() => handleDeleteProduct(product.id)}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {/* 分页控件 */}
            {pagination.total > 0 && (
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-6 space-y-4 sm:space-y-0">
                    <div className="text-sm text-apple-gray-600">
                        显示 {((pagination.current - 1) * pagination.size) + 1} - {Math.min(pagination.current * pagination.size, pagination.total)} 条，共 {pagination.total} 条
                    </div>
                    <div className="flex items-center space-x-2">
                        {/* 页面大小选择 */}
                        <select
                            value={pagination.size}
                            onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                            className="apple-input px-3 py-1 text-sm rounded-apple"
                        >
                            <option value={10}>10条/页</option>
                            <option value={50}>50条/页</option>
                        </select>

                        {/* 分页按钮 */}
                        {pagination.pages > 1 && (
                            <div className="flex items-center space-x-1">
                                <button
                                    onClick={() => handlePageChange(1)}
                                    disabled={pagination.current === 1}
                                    className="px-3 py-1 text-sm rounded-apple border border-gray-300 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    首页
                                </button>
                                <button
                                    onClick={() => handlePageChange(pagination.current - 1)}
                                    disabled={pagination.current === 1}
                                    className="px-3 py-1 text-sm rounded-apple border border-gray-300 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    上一页
                                </button>

                                {/* 页码按钮 */}
                                {renderPageNumbers()}

                                <button
                                    onClick={() => handlePageChange(pagination.current + 1)}
                                    disabled={pagination.current >= pagination.pages}
                                    className="px-3 py-1 text-sm rounded-apple border border-gray-300 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    下一页
                                </button>
                                <button
                                    onClick={() => handlePageChange(pagination.pages)}
                                    disabled={pagination.current >= pagination.pages}
                                    className="px-3 py-1 text-sm rounded-apple border border-gray-300 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    末页
                                </button>

                                {/* 快速跳转 */}
                                <div className="flex items-center space-x-2 ml-4">
                                    <span className="text-sm text-apple-gray-600">跳转到</span>
                                    <input
                                        type="number"
                                        min="1"
                                        max={pagination.pages}
                                        className="apple-input px-2 py-1 text-sm w-16 rounded-apple text-center"
                                        onKeyPress={(e) => {
                                            if (e.key === 'Enter') {
                                                const page = parseInt(e.target.value);
                                                if (page >= 1 && page <= pagination.pages) {
                                                    handlePageChange(page);
                                                    e.target.value = '';
                                                }
                                            }
                                        }}
                                        placeholder={pagination.current}
                                    />
                                    <span className="text-sm text-apple-gray-600">页</span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );

    const renderCategoriesTab = () => (
        <div className="space-y-6">
            {/* 工具栏 */}
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-apple-gray-900">商品分类管理</h3>
                <button
                    onClick={handleCreateCategory}
                    className="apple-button bg-apple-blue text-white px-4 py-2 rounded-apple hover:bg-blue-600 transition-colors"
                >
                    <i className="fas fa-plus mr-2"></i>
                    新增分类
                </button>
            </div>

            {/* 分类列表 */}
            {categories.length === 0 ? (
                <div className="text-center py-12">
                    <i className="fas fa-tags text-6xl text-apple-gray-300 mb-4"></i>
                    <h3 className="text-xl font-semibold text-apple-gray-900 mb-2">暂无分类</h3>
                    <p className="text-apple-gray-600 mb-4">还没有添加任何商品分类</p>
                    <button
                        onClick={handleCreateCategory}
                        className="apple-button bg-apple-blue text-white px-6 py-3 rounded-apple hover:bg-blue-600 transition-colors"
                    >
                        <i className="fas fa-plus mr-2"></i>
                        添加第一个分类
                    </button>
                </div>
            ) : (
                <div className="apple-card rounded-2xl overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-apple-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">分类名称</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">排序</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">商品数量</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {categories.map(category => (
                                    <tr key={category.id} className="hover:bg-apple-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-apple-gray-900">{category.name}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                            {category.sortOrder}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                            {products.filter(p => p.categoryId === category.id).length}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-500">
                                            {category.createdAt ? new Date(category.createdAt).toLocaleDateString() : '-'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={() => handleEditCategory(category)}
                                                    className="text-apple-blue hover:text-blue-600"
                                                >
                                                    编辑
                                                </button>
                                                <button
                                                    onClick={() => handleDeleteCategory(category.id)}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}
        </div>
    );

    return (
        <Layout title="库存管理" subtitle="商品和分类管理">
            <div className="space-y-6">
                {/* 标签页导航 */}
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        <button
                            onClick={() => setActiveTab('products')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'products'
                                    ? 'border-apple-blue text-apple-blue'
                                    : 'border-transparent text-apple-gray-500 hover:text-apple-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <i className="fas fa-box mr-2"></i>
                            商品管理
                        </button>
                        <button
                            onClick={() => setActiveTab('categories')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'categories'
                                    ? 'border-apple-blue text-apple-blue'
                                    : 'border-transparent text-apple-gray-500 hover:text-apple-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <i className="fas fa-tags mr-2"></i>
                            分类管理
                        </button>
                    </nav>
                </div>

                {/* 标签页内容 */}
                {activeTab === 'products' && renderProductsTab()}
                {activeTab === 'categories' && renderCategoriesTab()}
            </div>

            {/* 商品弹窗 */}
            <ProductModal
                visible={productModalVisible}
                onClose={() => setProductModalVisible(false)}
                onSuccess={() => {
                    fetchProducts();
                    setProductModalVisible(false);
                }}
                product={selectedProduct}
                type={productModalType}
            />

            {/* 分类弹窗 */}
            <CategoryModal
                visible={categoryModalVisible}
                onClose={() => setCategoryModalVisible(false)}
                onSuccess={() => {
                    fetchCategories();
                    setCategoryModalVisible(false);
                }}
                category={selectedCategory2}
                type={categoryModalType}
            />
        </Layout>
    );
};

export default Inventory;
