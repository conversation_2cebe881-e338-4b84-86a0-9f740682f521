import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import MemoModal from '../components/MemoModal';
import { useAuth } from '../contexts/AuthContext';
import { statisticsAPI, memoAPI } from '../services/api';

const Dashboard = () => {
    const { user } = useAuth();
    const [dashboardData, setDashboardData] = useState({
        todaySalesAmount: 0,
        salesGrowthPercentage: 0,
        todayOrderCount: 0,
        orderGrowthPercentage: 0,
        todayNewMemberCount: 0,
        memberGrowthPercentage: 0,
    });
    const [recentOrders, setRecentOrders] = useState([]);
    const [memos, setMemos] = useState([]);
    const [memoLoading, setMemoLoading] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showMemoModal, setShowMemoModal] = useState(false);

    // 获取备忘录数据
    const fetchMemos = async () => {
        try {
            setMemoLoading(true);
            const response = await memoAPI.queryMemos({
                current: 1,
                size: 5 // 只获取前5条
            });

            if (response.data.code === 200) {
                setMemos(response.data.data.records || []);
            } else {
                console.error('获取备忘录失败:', response.data.msg);
            }
        } catch (error) {
            console.error('获取备忘录失败:', error);
        } finally {
            setMemoLoading(false);
        }
    };

    useEffect(() => {
        // 获取今日统计数据和最近订单
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                // 并行获取统计数据、最近订单和备忘录
                const [statisticsResponse, ordersResponse] = await Promise.all([
                    statisticsAPI.getTodayStatistics(),
                    statisticsAPI.getRecentOrders()
                ]);

                // 处理统计数据
                if (statisticsResponse.data.code === 200) {
                    setDashboardData(statisticsResponse.data.data);
                } else {
                    console.error('获取统计数据失败:', statisticsResponse.data.msg);
                }

                // 处理最近订单数据
                if (ordersResponse.data.code === 200) {
                    setRecentOrders(ordersResponse.data.data || []);
                } else {
                    console.error('获取最近订单失败:', ordersResponse.data.msg);
                }

                // 获取备忘录数据
                fetchMemos();

            } catch (error) {
                console.error('获取数据失败:', error);
                setError('获取数据失败，请稍后重试');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    const formatCurrency = (amount) => {
        return `¥${Number(amount || 0).toLocaleString()}`;
    };

    const formatGrowth = (growth) => {
        const isPositive = growth > 0;
        return (
            <span className={`text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                <i className={`fas fa-arrow-${isPositive ? 'up' : 'down'} mr-1`}></i>
                {Math.abs(growth)}%
            </span>
        );
    };

    // 格式化订单商品列表
    const formatOrderItems = (items) => {
        if (!items || items.length === 0) return '无商品信息';
        return items.map(item => `${item.productName} x${item.quantity}`).join(', ');
    };

    // 格式化备忘录时间
    const formatMemoTime = (dateString) => {
        if (!dateString) return '';

        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;

        return date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit'
        });
    };

    // 格式化时间
    const formatOrderTime = (orderNo) => {
        // 从订单号中提取时间信息（假设订单号包含时间戳）
        // 这里可以根据实际的订单号格式进行调整
        try {
            const timeStr = orderNo.substring(0, 14); // 假设前14位是时间
            if (timeStr.length === 14) {
                const year = timeStr.substring(0, 4);
                const month = timeStr.substring(4, 6);
                const day = timeStr.substring(6, 8);
                const hour = timeStr.substring(8, 10);
                const minute = timeStr.substring(10, 12);

                const orderDate = new Date(`${year}-${month}-${day}T${hour}:${minute}:00`);
                const now = new Date();
                const diffMs = now - orderDate;
                const diffMins = Math.floor(diffMs / 60000);

                if (diffMins < 1) return '刚刚';
                if (diffMins < 60) return `${diffMins}分钟前`;
                if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
                return `${Math.floor(diffMins / 1440)}天前`;
            }
        } catch (error) {
            console.error('解析订单时间失败:', error);
        }
        return '未知时间';
    };

    const getCurrentDate = () => {
        const now = new Date();
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        return now.toLocaleDateString('zh-CN', options);
    };

    if (loading) {
        return (
            <Layout title="主页" subtitle="系统概览">
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-apple-blue mx-auto mb-4"></div>
                        <p className="text-apple-gray-600">加载中...</p>
                    </div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout title="主页" subtitle="系统概览">
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <i className="fas fa-exclamation-triangle text-6xl text-apple-red mb-4"></i>
                        <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2">加载失败</h2>
                        <p className="text-apple-gray-600 mb-4">{error}</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="apple-button px-6 py-2 rounded-apple"
                        >
                            重新加载
                        </button>
                    </div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout title="主页" subtitle="系统概览">
            {/* 欢迎区域 */}
            <div className="mb-8">
                <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2 font-sf-pro">
                    欢迎回来，{user?.fullName || user?.username || '管理员'}
                </h2>
                <p className="text-apple-gray-600">今天是 {getCurrentDate()}，祝您工作愉快！</p>
            </div>

            {/* 数据概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="apple-card rounded-2xl p-6 hover:shadow-apple-lg transition-all duration-300">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-apple-gray-600">今日销售额</p>
                            <p className="text-2xl font-semibold text-apple-gray-900 mt-1 font-sf-pro">
                                {formatCurrency(dashboardData.todaySalesAmount)}
                            </p>
                            {formatGrowth(dashboardData.salesGrowthPercentage)}
                        </div>
                        <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <i className="fas fa-yen-sign text-green-600 text-lg"></i>
                        </div>
                    </div>
                </div>

                <div className="apple-card rounded-2xl p-6 hover:shadow-apple-lg transition-all duration-300">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-apple-gray-600">今日订单数</p>
                            <p className="text-2xl font-semibold text-apple-gray-900 mt-1 font-sf-pro">
                                {dashboardData.todayOrderCount}
                            </p>
                            {formatGrowth(dashboardData.orderGrowthPercentage)}
                        </div>
                        <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i className="fas fa-receipt text-apple-blue text-lg"></i>
                        </div>
                    </div>
                </div>

                <div className="apple-card rounded-2xl p-6 hover:shadow-apple-lg transition-all duration-300">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-apple-gray-600">今日新增会员</p>
                            <p className="text-2xl font-semibold text-apple-gray-900 mt-1 font-sf-pro">
                                {dashboardData.todayNewMemberCount}
                            </p>
                            {formatGrowth(dashboardData.memberGrowthPercentage)}
                        </div>
                        <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                            <i className="fas fa-user-plus text-orange-600 text-lg"></i>
                        </div>
                    </div>
                </div>
            </div>

            {/* 快速功能入口 */}
            <div className="mb-8">
                <h3 className="text-xl font-semibold text-apple-gray-900 mb-6 font-sf-pro">快速功能</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <Link to="/pos" className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 block">
                        <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-cash-register text-apple-blue text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">收银台</h4>
                        <p className="text-xs text-apple-gray-500">开始收银</p>
                    </Link>

                    <Link to="/inventory" className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 block">
                        <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-plus text-green-600 text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">新增商品</h4>
                        <p className="text-xs text-apple-gray-500">添加商品</p>
                    </Link>

                    <Link to="/members" className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 block">
                        <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-user-plus text-purple-600 text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">新增会员</h4>
                        <p className="text-xs text-apple-gray-500">注册会员</p>
                    </Link>

                    <Link to="/reports" className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 block">
                        <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-chart-line text-orange-600 text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">销售报表</h4>
                        <p className="text-xs text-apple-gray-500">查看报表</p>
                    </Link>

                    <div className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 cursor-pointer">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-exclamation-triangle text-red-600 text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">库存预警</h4>
                        <p className="text-xs text-apple-gray-500">库存不足</p>
                    </div>

                    <Link to="/settings" className="apple-card rounded-2xl p-6 text-center hover:shadow-apple-lg transition-all duration-300 block">
                        <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i className="fas fa-cog text-apple-gray-600 text-lg"></i>
                        </div>
                        <h4 className="text-sm font-medium text-apple-gray-900 mb-1">系统设置</h4>
                        <p className="text-xs text-apple-gray-500">配置系统</p>
                    </Link>
                </div>
            </div>

            {/* 最近活动 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 最近订单 */}
                <div className="apple-card rounded-2xl p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-apple-gray-900 font-sf-pro">最近订单</h3>
                        <Link to="/pos" className="text-sm text-apple-blue hover:text-blue-800 transition-colors">查看全部</Link>
                    </div>
                    <div className="space-y-4">
                        {recentOrders.length === 0 ? (
                            <div className="text-center py-8">
                                <i className="fas fa-receipt text-4xl text-apple-gray-300 mb-2"></i>
                                <p className="text-apple-gray-500 text-sm">暂无最近订单</p>
                            </div>
                        ) : (
                            recentOrders.map((order, index) => (
                                <div key={order.orderNo || index} className="flex items-center justify-between py-3 border-b border-apple-gray-100 last:border-b-0">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i className="fas fa-check text-green-600"></i>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-apple-gray-900">#{order.orderNo}</p>
                                            <p className="text-xs text-apple-gray-500">{formatOrderItems(order.items)}</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-sm font-medium text-apple-gray-900">{formatCurrency(order.totalAmount)}</p>
                                        <p className="text-xs text-apple-gray-500">{formatOrderTime(order.orderNo)}</p>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                {/* 备忘录 */}
                <div className="apple-card rounded-2xl p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-apple-gray-900 font-sf-pro">备忘录</h3>
                        <button
                            onClick={() => setShowMemoModal(true)}
                            className="text-sm text-apple-blue hover:text-blue-800 transition-colors"
                        >
                            管理备忘录
                        </button>
                    </div>
                    <div className="space-y-4">
                        {memoLoading ? (
                            <div className="text-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue mx-auto mb-4"></div>
                                <p className="text-apple-gray-600">加载中...</p>
                            </div>
                        ) : memos.length === 0 ? (
                            <div className="text-center py-8">
                                <i className="fas fa-sticky-note text-4xl text-apple-gray-300 mb-2"></i>
                                <p className="text-apple-gray-500 text-sm mb-3">暂无备忘录</p>
                                <button
                                    onClick={() => setShowMemoModal(true)}
                                    className="px-4 py-2 bg-apple-blue text-white rounded-apple hover:bg-apple-blue/90 transition-colors text-sm"
                                >
                                    <i className="fas fa-plus mr-2"></i>
                                    添加备忘录
                                </button>
                            </div>
                        ) : (
                            <>
                                {memos.slice(0, 3).map((memo) => (
                                    <div key={memo.id} className="flex items-start space-x-3 py-3 border-b border-apple-gray-100 last:border-b-0">
                                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 flex-shrink-0 ${
                                            memo.status === 'DONE'
                                                ? 'bg-apple-green border-apple-green text-white'
                                                : 'border-apple-gray-300'
                                        }`}>
                                            {memo.status === 'DONE' && <i className="fas fa-check text-xs"></i>}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className={`text-sm ${
                                                memo.status === 'DONE'
                                                    ? 'text-apple-gray-500 line-through'
                                                    : 'text-apple-gray-900'
                                            } truncate`}>
                                                {memo.content}
                                            </p>
                                            <p className="text-xs text-apple-gray-400 mt-1">
                                                {formatMemoTime(memo.createdAt)}
                                            </p>
                                        </div>
                                    </div>
                                ))}

                                {memos.length > 3 && (
                                    <div className="text-center pt-2">
                                        <button
                                            onClick={() => setShowMemoModal(true)}
                                            className="text-sm text-apple-blue hover:text-blue-800 transition-colors"
                                        >
                                            查看全部 {memos.length} 条备忘录
                                        </button>
                                    </div>
                                )}

                                <div className="flex items-center justify-between pt-3 border-t border-apple-gray-100">
                                    <div className="text-xs text-apple-gray-500">
                                        共 {memos.length} 条，已完成 {memos.filter(m => m.status === 'DONE').length} 条
                                    </div>
                                    <button
                                        onClick={() => setShowMemoModal(true)}
                                        className="text-xs text-apple-blue hover:text-blue-800 transition-colors"
                                    >
                                        <i className="fas fa-plus mr-1"></i>
                                        添加
                                    </button>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* 备忘录模态框 */}
            <MemoModal
                isOpen={showMemoModal}
                onClose={() => {
                    setShowMemoModal(false);
                    // 关闭模态框后刷新备忘录数据
                    fetchMemos();
                }}
            />
        </Layout>
    );
};

export default Dashboard;
