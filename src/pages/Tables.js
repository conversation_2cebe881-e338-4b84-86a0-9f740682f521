import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import TableCard from '../components/TableCard';
import TableUsageModal from '../components/TableUsageModal';
import OpenTableModal from '../components/OpenTableModal';
import { tableAPI } from '../services/api';

const Tables = () => {
    const [tables, setTables] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showUsageModal, setShowUsageModal] = useState(false);
    const [showOpenModal, setShowOpenModal] = useState(false);
    const [selectedTable, setSelectedTable] = useState(null);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // 获取桌台列表
    const fetchTables = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await tableAPI.getTables();
            if (response.data.code === 200) {
                setTables(response.data.data || []);
            } else {
                setError(response.data.msg || '获取桌台列表失败');
            }
        } catch (err) {
            console.error('获取桌台列表失败:', err);
            setError('获取桌台列表失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 开台
    const handleOpenTable = async (tableId, orderId) => {
        try {
            const response = await tableAPI.openTable(tableId, orderId);
            if (response.data.code === 200) {
                setRefreshTrigger(prev => prev + 1);
                setShowOpenModal(false);
                setSelectedTable(null);
            } else {
                throw new Error(response.data.msg || '开台失败');
            }
        } catch (err) {
            console.error('开台失败:', err);
            alert(err.message || '开台失败，请稍后重试');
        }
    };

    // 关台
    const handleCloseTable = async (tableId) => {
        if (!window.confirm('确定要关台吗？')) {
            return;
        }

        try {
            const response = await tableAPI.closeTable(tableId);
            if (response.data.code === 200) {
                setRefreshTrigger(prev => prev + 1);
            } else {
                throw new Error(response.data.msg || '关台失败');
            }
        } catch (err) {
            console.error('关台失败:', err);
            alert(err.message || '关台失败，请稍后重试');
        }
    };

    // 查看使用记录
    const handleViewUsage = (table) => {
        setSelectedTable(table);
        setShowUsageModal(true);
    };

    // 打开开台模态框
    const handleOpenTableModal = (table) => {
        setSelectedTable(table);
        setShowOpenModal(true);
    };

    useEffect(() => {
        fetchTables();
    }, [refreshTrigger]); // eslint-disable-line react-hooks/exhaustive-deps

    // 计算统计数据
    const stats = {
        total: tables.length,
        open: tables.filter(table => table.isOpen).length,
        closed: tables.filter(table => !table.isOpen).length,
    };

    if (loading) {
        return (
            <Layout>
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-apple-blue mx-auto mb-4"></div>
                        <p className="text-apple-gray-600">加载中...</p>
                    </div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                        <i className="fas fa-exclamation-triangle text-6xl text-apple-red mb-4"></i>
                        <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2">加载失败</h2>
                        <p className="text-apple-gray-600 mb-4">{error}</p>
                        <button 
                            onClick={fetchTables}
                            className="apple-button px-6 py-2 rounded-apple"
                        >
                            重新加载
                        </button>
                    </div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="p-6">
                {/* 页面标题 */}
                <div className="mb-8">
                    <h1 className="text-3xl font-semibold text-apple-gray-900 mb-2">桌台管理</h1>
                    <p className="text-apple-gray-600">管理门店桌台状态和使用记录</p>
                </div>

                {/* 统计卡片 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="apple-card rounded-2xl p-6">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-apple-blue/10 rounded-xl flex items-center justify-center mr-4">
                                <i className="fas fa-table text-apple-blue text-xl"></i>
                            </div>
                            <div>
                                <p className="text-sm text-apple-gray-600">总桌台数</p>
                                <p className="text-2xl font-semibold text-apple-gray-900">{stats.total}</p>
                            </div>
                        </div>
                    </div>

                    <div className="apple-card rounded-2xl p-6">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-apple-green/10 rounded-xl flex items-center justify-center mr-4">
                                <i className="fas fa-play-circle text-apple-green text-xl"></i>
                            </div>
                            <div>
                                <p className="text-sm text-apple-gray-600">已开台</p>
                                <p className="text-2xl font-semibold text-apple-gray-900">{stats.open}</p>
                            </div>
                        </div>
                    </div>

                    <div className="apple-card rounded-2xl p-6">
                        <div className="flex items-center">
                            <div className="w-12 h-12 bg-apple-gray-100 rounded-xl flex items-center justify-center mr-4">
                                <i className="fas fa-pause-circle text-apple-gray-500 text-xl"></i>
                            </div>
                            <div>
                                <p className="text-sm text-apple-gray-600">空闲桌台</p>
                                <p className="text-2xl font-semibold text-apple-gray-900">{stats.closed}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-apple-gray-900">桌台列表</h2>
                    <div className="flex space-x-3">
                        <button
                            onClick={() => setShowUsageModal(true)}
                            className="flex items-center px-4 py-2 text-apple-blue border border-apple-blue rounded-apple hover:bg-apple-blue/5 transition-colors"
                        >
                            <i className="fas fa-history mr-2"></i>
                            使用记录
                        </button>
                        <button
                            onClick={fetchTables}
                            className="flex items-center px-4 py-2 bg-apple-blue text-white rounded-apple hover:bg-apple-blue/90 transition-colors"
                        >
                            <i className="fas fa-sync-alt mr-2"></i>
                            刷新
                        </button>
                    </div>
                </div>

                {/* 桌台网格 */}
                {tables.length === 0 ? (
                    <div className="text-center py-12">
                        <i className="fas fa-table text-6xl text-apple-gray-300 mb-4"></i>
                        <h3 className="text-xl font-semibold text-apple-gray-900 mb-2">暂无桌台</h3>
                        <p className="text-apple-gray-600">请联系管理员添加桌台</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {tables.map((table) => (
                            <TableCard
                                key={table.id}
                                table={table}
                                onOpen={() => handleOpenTableModal(table)}
                                onClose={() => handleCloseTable(table.id)}
                                onViewUsage={() => handleViewUsage(table)}
                            />
                        ))}
                    </div>
                )}

                {/* 使用记录模态框 */}
                {showUsageModal && (
                    <TableUsageModal
                        table={selectedTable}
                        onClose={() => {
                            setShowUsageModal(false);
                            setSelectedTable(null);
                        }}
                    />
                )}

                {/* 开台模态框 */}
                {showOpenModal && selectedTable && (
                    <OpenTableModal
                        table={selectedTable}
                        onClose={() => {
                            setShowOpenModal(false);
                            setSelectedTable(null);
                        }}
                        onConfirm={handleOpenTable}
                    />
                )}
            </div>
        </Layout>
    );
};

export default Tables;
