import React, { useState, useEffect, useCallback } from 'react';
import Layout from '../components/Layout';
import { memberAPI, membershipLevelAPI } from '../services/api';
import MemberModal from '../components/MemberModal';
import BalanceModal from '../components/BalanceModal';
import RecordsModal from '../components/RecordsModal';
import RechargeRecordsModal from '../components/RechargeRecordsModal';
import { safeProcessLargeIntegers, formatMemberId } from '../utils/numberUtils';

const Members = () => {
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });

    // 搜索条件
    const [searchForm, setSearchForm] = useState({
        nickname: '',
        phoneNumber: '',
        membershipLevelId: ''
    });

    // 模态框状态
    const [memberModal, setMemberModal] = useState({
        visible: false,
        type: 'create', // create, edit, view
        data: null
    });

    const [balanceModal, setBalanceModal] = useState({
        visible: false,
        member: null
    });

    const [recordsModal, setRecordsModal] = useState({
        visible: false,
        type: '', // consumption, points
        member: null
    });

    const [rechargeRecordsModal, setRechargeRecordsModal] = useState({
        visible: false,
        member: null
    });

    // 会员等级选项
    const [membershipLevels, setMembershipLevels] = useState([]);

    // 获取会员列表
    const fetchMembers = useCallback(async () => {
        setLoading(true);
        try {
            const response = await memberAPI.queryMembers({
                current: pagination.current,
                size: pagination.size,
                ...searchForm
            });

            if (response.data.code === 200) {
                const responseData = response.data.data;
                console.log('会员列表分页数据:', responseData);

                // 处理会员数据，确保大整数字段为字符串
                const processedMembers = safeProcessLargeIntegers(responseData.records || [], ['memberId', 'id']);

                setMembers(processedMembers);
                setPagination(prev => ({
                    ...prev,
                    total: responseData.total || 0,
                    pages: responseData.pages || 0
                }));
            }
        } catch (error) {
            console.error('获取会员列表失败:', error);
        } finally {
            setLoading(false);
        }
    }, [pagination.current, pagination.size, searchForm]);

    // 获取会员等级列表
    const fetchMembershipLevels = useCallback(async () => {
        try {
            const response = await membershipLevelAPI.getActiveLevels();
            if (response.data.code === 200) {
                const levels = response.data.data.map(level => ({
                    id: level.id,
                    name: level.levelName,
                    tag: level.levelTag,
                    color: getColorByTag(level.levelTag)
                }));
                setMembershipLevels(levels);
            }
        } catch (error) {
            console.error('获取会员等级失败:', error);
            // 如果API失败，使用默认等级
            setMembershipLevels([
                { id: 1, name: '普通会员', tag: 'normal', color: 'gray' }
            ]);
        }
    }, []);

    // 根据等级标签获取颜色
    const getColorByTag = (tag) => {
        const colorMap = {
            'normal': 'gray',
            'silver': 'gray',
            'gold': 'yellow',
            'diamond': 'blue',
            'vip': 'purple'
        };
        return colorMap[tag] || 'gray';
    };

    useEffect(() => {
        fetchMembers();
        fetchMembershipLevels();
    }, [fetchMembers, fetchMembershipLevels]);

    // 搜索处理
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchMembers();
    };

    // 重置搜索
    const handleReset = () => {
        setSearchForm({
            nickname: '',
            phoneNumber: '',
            membershipLevelId: ''
        });
        setPagination(prev => ({ ...prev, current: 1 }));
        setTimeout(fetchMembers, 100);
    };

    // 分页处理
    const handlePageChange = (page) => {
        setPagination(prev => ({ ...prev, current: page }));
    };

    // 获取会员等级信息
    const getMembershipLevel = (levelId) => {
        return membershipLevels.find(level => level.id === levelId) || { name: '未知', color: 'gray' };
    };

    // 格式化金额
    const formatCurrency = (amount) => {
        return `¥${Number(amount || 0).toFixed(2)}`;
    };

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 打开会员详情模态框
    const openMemberModal = (type, member = null) => {
        setMemberModal({
            visible: true,
            type,
            data: member
        });
    };

    // 关闭会员模态框
    const closeMemberModal = () => {
        setMemberModal({
            visible: false,
            type: 'create',
            data: null
        });
    };

    // 打开充值模态框
    const openBalanceModal = (member) => {
        setBalanceModal({
            visible: true,
            member
        });
    };

    // 关闭充值模态框
    const closeBalanceModal = () => {
        setBalanceModal({
            visible: false,
            member: null
        });
    };

    // 打开记录模态框
    const openRecordsModal = (type, member) => {
        setRecordsModal({
            visible: true,
            type,
            member
        });
    };

    // 关闭记录模态框
    const closeRecordsModal = () => {
        setRecordsModal({
            visible: false,
            type: '',
            member: null
        });
    };

    // 打开充值记录模态框
    const openRechargeRecordsModal = (member) => {
        setRechargeRecordsModal({
            visible: true,
            member
        });
    };

    // 关闭充值记录模态框
    const closeRechargeRecordsModal = () => {
        setRechargeRecordsModal({
            visible: false,
            member: null
        });
    };

    // 会员操作成功后刷新列表
    const handleMemberSuccess = () => {
        fetchMembers();
        closeMemberModal();
    };

    // 充值成功后刷新列表
    const handleBalanceSuccess = () => {
        fetchMembers();
        closeBalanceModal();
    };

    return (
        <Layout title="会员管理" subtitle="会员信息管理">
            {/* 搜索区域 */}
            <div className="apple-card rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 mb-4">
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            会员名称
                        </label>
                        <input
                            type="text"
                            placeholder="请输入会员名称"
                            value={searchForm.nickname}
                            onChange={(e) => setSearchForm(prev => ({ ...prev, nickname: e.target.value }))}
                            className="apple-input w-full px-3 py-2 rounded-apple"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            手机号
                        </label>
                        <input
                            type="text"
                            placeholder="请输入手机号"
                            value={searchForm.phoneNumber}
                            onChange={(e) => setSearchForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                            className="apple-input w-full px-3 py-2 rounded-apple"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">
                            会员等级
                        </label>
                        <select
                            value={searchForm.membershipLevelId}
                            onChange={(e) => setSearchForm(prev => ({ ...prev, membershipLevelId: e.target.value }))}
                            className="apple-input w-full px-3 py-2 rounded-apple"
                        >
                            <option value="">全部等级</option>
                            {membershipLevels.map(level => (
                                <option key={level.id} value={level.id}>
                                    {level.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="flex items-end">
                        <button
                            onClick={handleSearch}
                            className="apple-button px-4 py-2 rounded-apple w-full"
                        >
                            <i className="fas fa-search mr-2"></i>
                            搜索
                        </button>
                    </div>
                    <div className="flex items-end">
                        <button
                            onClick={handleReset}
                            className="bg-apple-gray-200 text-apple-gray-700 px-4 py-2 rounded-apple hover:bg-apple-gray-300 transition-colors w-full"
                        >
                            重置
                        </button>
                    </div>
                </div>

                <div className="flex justify-between items-center">
                    <div className="text-sm text-apple-gray-600">
                        共找到 {pagination.total} 位会员
                        <span className="ml-2 text-xs text-apple-gray-400">
                            (第 {pagination.current} 页，共 {pagination.pages} 页，每页 {pagination.size} 条)
                        </span>
                    </div>
                    <button
                        onClick={() => openMemberModal('create')}
                        className="apple-button px-4 py-2 rounded-apple"
                    >
                        <i className="fas fa-plus mr-2"></i>
                        新增会员
                    </button>
                </div>
            </div>

            {/* 会员列表 */}
            <div className="apple-card rounded-2xl overflow-hidden">
                {loading ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-apple-blue"></div>
                        <span className="ml-2 text-apple-gray-600">加载中...</span>
                    </div>
                ) : (
                    <>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-apple-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            会员信息
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            等级
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            余额
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            积分
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            注册时间
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-apple-gray-500 uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-apple-gray-200">
                                    {members.map((member) => {
                                        const level = getMembershipLevel(member.membershipLevelId);
                                        return (
                                            <tr key={member.id} className="hover:bg-apple-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="flex-shrink-0 h-10 w-10">
                                                            {member.avatarUrl ? (
                                                                <img
                                                                    className="h-10 w-10 rounded-full"
                                                                    src={member.avatarUrl}
                                                                    alt={member.nickname}
                                                                />
                                                            ) : (
                                                                <div className="h-10 w-10 rounded-full bg-apple-gray-300 flex items-center justify-center">
                                                                    <i className="fas fa-user text-apple-gray-600"></i>
                                                                </div>
                                                            )}
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-apple-gray-900">
                                                                {member.nickname || '未设置'}
                                                            </div>
                                                            <div className="text-sm text-apple-gray-500">
                                                                {member.phoneNumber || '未绑定手机'}
                                                            </div>
                                                            <div className="text-xs text-apple-gray-400">
                                                                ID: {formatMemberId(member.memberId)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${level.color}-100 text-${level.color}-800`}>
                                                        {level.name}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {formatCurrency(member.balance)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-900">
                                                    {member.points || 0}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-apple-gray-500">
                                                    {formatDate(member.createdAt)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex justify-end space-x-2">
                                                        <button
                                                            onClick={() => openRecordsModal('points', member)}
                                                            className="text-apple-blue hover:text-blue-800 transition-colors"
                                                            title="查看积分记录"
                                                        >
                                                            <i className="fas fa-star"></i>
                                                        </button>
                                                        <button
                                                            onClick={() => openBalanceModal(member)}
                                                            className="text-green-600 hover:text-green-800 transition-colors"
                                                            title="充值"
                                                        >
                                                            <i className="fas fa-plus-circle"></i>
                                                        </button>
                                                        <button
                                                            onClick={() => openRechargeRecordsModal(member)}
                                                            className="text-purple-600 hover:text-purple-800 transition-colors"
                                                            title="充值记录"
                                                        >
                                                            <i className="fas fa-credit-card"></i>
                                                        </button>
                                                        <button
                                                            onClick={() => openRecordsModal('consumption', member)}
                                                            className="text-orange-600 hover:text-orange-800 transition-colors"
                                                            title="消费记录"
                                                        >
                                                            <i className="fas fa-receipt"></i>
                                                        </button>
                                                        <button
                                                            onClick={() => openMemberModal('edit', member)}
                                                            className="text-apple-blue hover:text-blue-800 transition-colors"
                                                            title="修改信息"
                                                        >
                                                            <i className="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>

                        {/* 分页 - 始终显示分页信息 */}
                        {pagination.total > 0 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-apple-gray-200 sm:px-6">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => handlePageChange(pagination.current - 1)}
                                        disabled={pagination.current <= 1}
                                        className="relative inline-flex items-center px-4 py-2 border border-apple-gray-300 text-sm font-medium rounded-md text-apple-gray-700 bg-white hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        上一页
                                    </button>
                                    <button
                                        onClick={() => handlePageChange(pagination.current + 1)}
                                        disabled={pagination.current >= pagination.pages}
                                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-apple-gray-300 text-sm font-medium rounded-md text-apple-gray-700 bg-white hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        下一页
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-apple-gray-700">
                                            显示第 <span className="font-medium">{(pagination.current - 1) * pagination.size + 1}</span> 到{' '}
                                            <span className="font-medium">
                                                {Math.min(pagination.current * pagination.size, pagination.total)}
                                            </span>{' '}
                                            条，共 <span className="font-medium">{pagination.total}</span> 条记录
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <button
                                                onClick={() => handlePageChange(pagination.current - 1)}
                                                disabled={pagination.current <= 1}
                                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-apple-gray-300 bg-white text-sm font-medium text-apple-gray-500 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                <i className="fas fa-chevron-left"></i>
                                            </button>
                                            {pagination.pages > 0 && Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                                                let pageNum;
                                                if (pagination.pages <= 5) {
                                                    pageNum = i + 1;
                                                } else if (pagination.current <= 3) {
                                                    pageNum = i + 1;
                                                } else if (pagination.current >= pagination.pages - 2) {
                                                    pageNum = pagination.pages - 4 + i;
                                                } else {
                                                    pageNum = pagination.current - 2 + i;
                                                }

                                                return (
                                                    <button
                                                        key={pageNum}
                                                        onClick={() => handlePageChange(pageNum)}
                                                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                            pageNum === pagination.current
                                                                ? 'z-10 bg-apple-blue border-apple-blue text-white'
                                                                : 'bg-white border-apple-gray-300 text-apple-gray-500 hover:bg-apple-gray-50'
                                                        }`}
                                                    >
                                                        {pageNum}
                                                    </button>
                                                );
                                            })}
                                            <button
                                                onClick={() => handlePageChange(pagination.current + 1)}
                                                disabled={pagination.current >= pagination.pages}
                                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-apple-gray-300 bg-white text-sm font-medium text-apple-gray-500 hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                <i className="fas fa-chevron-right"></i>
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* 会员信息模态框 */}
            {memberModal.visible && (
                <MemberModal
                    visible={memberModal.visible}
                    type={memberModal.type}
                    data={memberModal.data}
                    membershipLevels={membershipLevels}
                    onClose={closeMemberModal}
                    onSuccess={handleMemberSuccess}
                />
            )}

            {/* 充值模态框 */}
            {balanceModal.visible && (
                <BalanceModal
                    visible={balanceModal.visible}
                    member={balanceModal.member}
                    onClose={closeBalanceModal}
                    onSuccess={handleBalanceSuccess}
                />
            )}

            {/* 记录查看模态框 */}
            {recordsModal.visible && (
                <RecordsModal
                    visible={recordsModal.visible}
                    type={recordsModal.type}
                    member={recordsModal.member}
                    onClose={closeRecordsModal}
                />
            )}

            {/* 充值记录模态框 */}
            {rechargeRecordsModal.visible && (
                <RechargeRecordsModal
                    visible={rechargeRecordsModal.visible}
                    member={rechargeRecordsModal.member}
                    onClose={closeRechargeRecordsModal}
                />
            )}
        </Layout>
    );
};

export default Members;
