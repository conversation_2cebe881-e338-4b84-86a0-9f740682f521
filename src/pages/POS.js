import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import MemberSearchModal from '../components/MemberSearchModal';
import OrderModal from '../components/OrderModal';
import { productAPI, orderAPI, tableAPI } from '../services/api';

// 支付方式枚举
const PAYMENT_METHODS = {
    CASH: { value: 'cash', label: '现金支付', description: '线下现金支付' },
    BALANCE: { value: 'balance', label: '余额支付', description: '会员账户余额支付' },
    WECHAT_PAY: { value: 'wechat_pay', label: '微信支付', description: '微信扫码或小程序支付' },
    OFFLINE_SCAN: { value: 'offline_scan', label: '会员线下扫码', description: '会员线下扫码支付（支付宝、微信等）' }
};

const POS = () => {
    // 主要状态
    const [activeTab, setActiveTab] = useState('cashier'); // cashier, orders
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState(['全部']);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [cart, setCart] = useState([]);
    const [isProcessing, setIsProcessing] = useState(false);

    // 会员相关
    const [selectedMember, setSelectedMember] = useState(null);
    const [memberSearchModal, setMemberSearchModal] = useState(false);

    // 桌台相关
    const [tables, setTables] = useState([]);
    const [selectedTable, setSelectedTable] = useState(null);

    // 优惠相关
    const [discounts, setDiscounts] = useState([]);
    const [staffDiscount, setStaffDiscount] = useState(0);
    const [couponCode, setCouponCode] = useState('');
    const [orderRemark, setOrderRemark] = useState('');

    // 支付方式相关
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('cash');
    // const [balancePaymentAmount, setBalancePaymentAmount] = useState(0); // 预留给混合支付功能

    // 订单管理相关
    const [orders, setOrders] = useState([]);
    const [ordersPagination, setOrdersPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });
    const [ordersLoading, setOrdersLoading] = useState(false);
    const [orderModal, setOrderModal] = useState({
        visible: false,
        orderId: null
    });

    // 订单查询条件
    const [orderSearchForm, setOrderSearchForm] = useState({
        status: '',
        paymentStatus: '',
        tableId: '',
        phoneNumber: '',
        startTime: '',
        endTime: ''
    });

    useEffect(() => {
        // 获取顶级分类
        const fetchCategories = async () => {
            try {
                const response = await productAPI.getTopLevelCategories();
                if (response.data.code === 200) {
                    const categoryData = response.data.data || [];
                    setCategories([
                        { id: 'all', name: '全部' },
                        ...categoryData
                    ]);

                    // 默认加载全部商品（第一个分类的商品）
                    if (categoryData.length > 0) {
                        fetchProductsByCategory(categoryData[0].id);
                    }
                }
            } catch (error) {
                console.error('获取商品分类失败:', error);
            }
        };

        // 获取桌台列表
        const fetchTables = async () => {
            try {
                const response = await tableAPI.getTables();
                if (response.data.code === 200) {
                    setTables(response.data.data || []);
                }
            } catch (error) {
                console.error('获取桌台列表失败:', error);
            }
        };

        fetchCategories();
        fetchTables();

        // 如果当前是订单管理标签页，加载订单列表
        if (activeTab === 'orders') {
            fetchOrders();
        }
    }, [activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

    // 根据分类获取商品
    const fetchProductsByCategory = async (categoryId) => {
        try {
            if (categoryId === 'all') {
                // 获取所有分类的商品
                const allProducts = [];
                for (const category of categories) {
                    if (category.id !== 'all') {
                        const response = await productAPI.getProductsByCategory(category.id);
                        if (response.data.code === 200) {
                            allProducts.push(...(response.data.data || []));
                        }
                    }
                }
                setProducts(allProducts);
            } else {
                const response = await productAPI.getProductsByCategory(categoryId);
                if (response.data.code === 200) {
                    setProducts(response.data.data || []);
                }
            }
        } catch (error) {
            console.error('获取商品列表失败:', error);
        }
    };

    // 处理分类切换
    const handleCategoryChange = (categoryId) => {
        setSelectedCategory(categoryId);
        fetchProductsByCategory(categoryId);
    };

    // 商品已经根据分类过滤，直接使用
    const filteredProducts = products;

    // 添加到购物车
    const addToCart = (product) => {
        const existingItem = cart.find(item => item.id === product.id);
        if (existingItem) {
            setCart(cart.map(item => 
                item.id === product.id 
                    ? { ...item, quantity: item.quantity + 1 }
                    : item
            ));
        } else {
            setCart([...cart, { ...product, quantity: 1 }]);
        }
    };

    // 更新商品数量
    const updateQuantity = (id, change) => {
        setCart(cart.map(item => {
            if (item.id === id) {
                const newQuantity = item.quantity + change;
                return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;
            }
            return item;
        }).filter(Boolean));
    };

    // 移除商品 (暂时注释，后续可能使用)
    // const removeFromCart = (id) => {
    //     setCart(cart.filter(item => item.id !== id));
    // };

    // 清空购物车
    const clearCart = () => {
        setCart([]);
        setSelectedMember(null);
        setSelectedTable(null);
        setDiscounts([]);
        setStaffDiscount(0);
        setCouponCode('');
        setOrderRemark('');
        setSelectedPaymentMethod('cash');
        // setBalancePaymentAmount(0); // 预留给混合支付功能
    };

    // 会员相关函数
    const handleSelectMember = (member) => {
        setSelectedMember(member);
        // 会员优惠暂时默认为0，不自动计算
        // 后续可以根据会员等级或其他规则计算优惠
    };

    const removeMember = () => {
        setSelectedMember(null);
        // 移除会员相关的优惠（如果有的话）
        setDiscounts(discounts.filter(d => d.discountType !== 'MEMBER'));
    };

    // 优惠券相关函数
    const applyCoupon = () => {
        if (!couponCode.trim()) {
            alert('请输入优惠券代码');
            return;
        }

        // 检查是否已经使用了优惠券
        if (discounts.some(d => d.discountType === 'COUPON')) {
            alert('已使用优惠券，请先移除后再添加新的优惠券');
            return;
        }

        // 这里应该调用API验证优惠券
        // 暂时模拟优惠券验证
        const couponDiscount = {
            discountType: 'COUPON',
            description: `优惠券: ${couponCode}`,
            amount: 10, // 模拟固定10元优惠
            referenceId: couponCode
        };

        setDiscounts([...discounts, couponDiscount]);
        setCouponCode('');
        alert('优惠券应用成功');
    };



    // 移除指定类型的优惠
    const removeDiscount = (discountType) => {
        setDiscounts(discounts.filter(d => d.discountType !== discountType));
    };



    // 获取订单列表
    const fetchOrders = async (page = 1) => {
        setOrdersLoading(true);
        try {
            const queryData = {
                current: page,
                size: ordersPagination.size,
                ...orderSearchForm
            };

            const response = await orderAPI.queryOrders(queryData);
            if (response.data.code === 200) {
                setOrders(response.data.data.records || []);
                setOrdersPagination({
                    current: response.data.data.current,
                    size: response.data.data.size,
                    total: response.data.data.total,
                    pages: response.data.data.pages
                });
            }
        } catch (error) {
            console.error('获取订单列表失败:', error);
        } finally {
            setOrdersLoading(false);
        }
    };

    // 搜索订单
    const handleOrderSearch = () => {
        fetchOrders(1);
    };

    // 重置搜索条件
    const resetOrderSearch = () => {
        setOrderSearchForm({
            status: '',
            paymentStatus: '',
            tableId: '',
            phoneNumber: '',
            startTime: '',
            endTime: ''
        });
        fetchOrders(1);
    };

    // 查看订单详情
    const handleViewOrder = (orderId) => {
        setOrderModal({
            visible: true,
            orderId: orderId
        });
    };

    // 订单更新回调
    const handleOrderUpdate = () => {
        fetchOrders(ordersPagination.current);
    };

    // 计算总价
    const calculateTotal = () => {
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // 计算总优惠金额
        const memberDiscountAmount = discounts.reduce((sum, discount) => sum + (discount.amount || 0), 0);
        const totalDiscountAmount = memberDiscountAmount + staffDiscount;

        const total = Math.max(0, subtotal - totalDiscountAmount);

        return {
            subtotal,
            discountAmount: totalDiscountAmount,
            total
        };
    };

    // 校验余额支付
    const validateBalancePayment = () => {
        if (selectedPaymentMethod === 'balance') {
            if (!selectedMember) {
                alert('请先选择会员');
                return false;
            }

            const { total } = calculateTotal();
            const memberBalance = selectedMember.balance || 0;

            if (memberBalance < total) {
                alert(`会员余额不足！当前余额：¥${memberBalance.toFixed(2)}，需要支付：¥${total.toFixed(2)}`);
                return false;
            }
        }
        return true;
    };

    // 获取支付方式显示信息
    const getPaymentMethodDisplay = (paymentMethod) => {
        const method = Object.values(PAYMENT_METHODS).find(m => m.value === paymentMethod);
        return method ? method.label : paymentMethod || '未知';
    };

    // 处理支付
    const handlePayment = async () => {
        if (cart.length === 0) {
            alert('购物车为空');
            return;
        }

        // 校验余额支付
        if (!validateBalancePayment()) {
            return;
        }

        setIsProcessing(true);
        try {
            // 构建优惠数组，只包含金额大于0的优惠
            const orderDiscounts = [];

            // 添加会员优惠
            discounts.forEach(discount => {
                if (discount.amount > 0) {
                    orderDiscounts.push(discount);
                }
            });

            // 添加员工调整优惠
            if (staffDiscount > 0) {
                orderDiscounts.push({
                    discountType: 'MANUAL',
                    description: '员工调整',
                    amount: staffDiscount
                });
            }

            // 构建订单数据
            const orderData = {
                orderType: 'DINE_IN', // 默认堂食
                items: cart.map(item => ({
                    productId: item.id,
                    quantity: item.quantity
                })),
                memberId: selectedMember ? selectedMember.memberId : null,
                tableId: selectedTable ? selectedTable.id : null,
                remark: orderRemark,
                paymentMethod: selectedPaymentMethod
            };

            // 只有当存在优惠时才添加discounts字段
            if (orderDiscounts.length > 0) {
                orderData.discounts = orderDiscounts;
            }

            const response = await orderAPI.createOrder(orderData);
            if (response.data.code === 200) {
                alert('订单创建成功！');
                clearCart();
                // 如果在订单管理页面，刷新订单列表
                if (activeTab === 'orders') {
                    fetchOrders(ordersPagination.current);
                }
            } else {
                alert('创建订单失败：' + response.data.msg);
            }
        } catch (error) {
            console.error('创建订单失败:', error);
            alert('创建订单失败，请重试');
        } finally {
            setIsProcessing(false);
        }
    };



    const getColorClasses = (color) => {
        const colorMap = {
            green: 'bg-green-100 text-green-600',
            orange: 'bg-orange-100 text-orange-600',
            red: 'bg-red-100 text-red-600',
            purple: 'bg-purple-100 text-purple-600',
            blue: 'bg-blue-100 text-apple-blue',
        };
        return colorMap[color] || 'bg-gray-100 text-apple-gray-600';
    };

    return (
        <Layout title="收银台" subtitle="点单收银">
            {/* 标签页切换 */}
            <div className="flex space-x-1 mb-6 bg-apple-gray-100 rounded-lg p-1">
                <button
                    onClick={() => setActiveTab('cashier')}
                    className={`flex-1 py-2 px-4 rounded-md font-medium transition-all duration-200 ${
                        activeTab === 'cashier'
                            ? 'bg-white text-apple-blue shadow-sm'
                            : 'text-apple-gray-600 hover:text-apple-gray-900'
                    }`}
                >
                    <i className="fas fa-cash-register mr-2"></i>
                    收银台
                </button>
                <button
                    onClick={() => setActiveTab('orders')}
                    className={`flex-1 py-2 px-4 rounded-md font-medium transition-all duration-200 ${
                        activeTab === 'orders'
                            ? 'bg-white text-apple-blue shadow-sm'
                            : 'text-apple-gray-600 hover:text-apple-gray-900'
                    }`}
                >
                    <i className="fas fa-list-alt mr-2"></i>
                    订单管理
                </button>
            </div>

            {activeTab === 'cashier' ? (
                <div className="flex flex-col lg:flex-row h-full gap-6">
                {/* 商品选择区域 */}
                <div className="flex-1">
                    {/* 分类标签 */}
                    <div className="flex flex-wrap gap-3 mb-6">
                        {categories.map((category) => (
                            <button
                                key={category.id}
                                onClick={() => handleCategoryChange(category.id)}
                                className={`px-4 py-2 rounded-apple font-medium transition-all duration-200 ${
                                    selectedCategory === category.id
                                        ? 'bg-apple-blue text-white shadow-md'
                                        : 'bg-apple-gray-100 text-apple-gray-700 hover:bg-apple-gray-200'
                                }`}
                            >
                                {category.name}
                            </button>
                        ))}
                    </div>

                    {/* 商品网格 */}
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {filteredProducts.map((product) => (
                            <div
                                key={product.id}
                                onClick={() => addToCart(product)}
                                className="apple-card rounded-2xl p-4 cursor-pointer hover:shadow-apple-lg transition-all duration-300 transform hover:-translate-y-1"
                            >
                                <div className={`aspect-square rounded-xl mb-3 flex items-center justify-center ${getColorClasses(product.color)}`}>
                                    <i className={`${product.icon} text-2xl`}></i>
                                </div>
                                <h3 className="font-medium text-apple-gray-900 mb-1 truncate">{product.name}</h3>
                                <p className="text-sm text-apple-gray-500 mb-2 truncate">{product.description}</p>
                                <p className="text-lg font-semibold text-apple-blue font-sf-pro">¥{product.price}</p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* 购物车区域 */}
                <div className="w-full lg:w-80 apple-card rounded-2xl p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-apple-gray-900 font-sf-pro">当前订单</h3>
                        {cart.length > 0 && (
                            <button
                                onClick={clearCart}
                                className="text-sm text-red-600 hover:text-red-800 transition-colors"
                            >
                                清空
                            </button>
                        )}
                    </div>

                    {/* 会员信息 */}
                    <div className="mb-4 p-3 bg-apple-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-apple-gray-700">会员信息</span>
                            <button
                                onClick={() => setMemberSearchModal(true)}
                                className="text-xs text-apple-blue hover:text-blue-600 transition-colors"
                            >
                                <i className="fas fa-search mr-1"></i>
                                查找会员
                            </button>
                        </div>
                        {selectedMember ? (
                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <div className="text-sm">
                                        <div className="text-apple-gray-900">{selectedMember.nickname || '未设置'}</div>
                                        <div className="text-apple-gray-500">{selectedMember.phoneNumber}</div>
                                    </div>
                                    <button
                                        onClick={removeMember}
                                        className="text-red-500 hover:text-red-700 transition-colors"
                                    >
                                        <i className="fas fa-times"></i>
                                    </button>
                                </div>
                                <div className="flex items-center justify-between text-sm pt-2 border-t border-apple-gray-200">
                                    <span className="text-apple-gray-600">账户余额:</span>
                                    <span className="text-apple-blue font-medium">¥{(selectedMember.balance || 0).toFixed(2)}</span>
                                </div>
                            </div>
                        ) : (
                            <div className="text-sm text-apple-gray-500">未选择会员</div>
                        )}
                    </div>

                    {/* 桌台选择 */}
                    {tables.length > 0 && (
                        <div className="mb-4 p-3 bg-apple-gray-50 rounded-lg">
                            <label className="block text-sm font-medium text-apple-gray-700 mb-2">桌台</label>
                            <select
                                value={selectedTable?.id || ''}
                                onChange={(e) => {
                                    const table = tables.find(t => t.id === parseInt(e.target.value));
                                    setSelectedTable(table || null);
                                }}
                                className="w-full px-3 py-2 text-sm border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                            >
                                <option value="">请选择桌台</option>
                                {tables.filter(table => table.isActive).map(table => (
                                    <option key={table.id} value={table.id}>
                                        {table.tableNumber}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}

                    {/* 优惠券 */}
                    <div className="mb-4 p-3 bg-apple-gray-50 rounded-lg">
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">优惠券</label>
                        <div className="flex space-x-2">
                            <input
                                type="text"
                                value={couponCode}
                                onChange={(e) => setCouponCode(e.target.value)}
                                placeholder="请输入优惠券代码"
                                className="flex-1 px-3 py-2 text-sm border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                            />
                            <button
                                onClick={applyCoupon}
                                className="px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"
                            >
                                应用
                            </button>
                        </div>
                    </div>

                    {/* 员工调整 */}
                    <div className="mb-4 p-3 bg-apple-gray-50 rounded-lg">
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">员工调整</label>
                        <input
                            type="number"
                            value={staffDiscount}
                            onChange={(e) => setStaffDiscount(parseFloat(e.target.value) || 0)}
                            placeholder="优惠金额"
                            className="w-full px-3 py-2 text-sm border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                            min="0"
                            step="0.01"
                        />
                    </div>

                    {/* 优惠汇总 */}
                    {(discounts.length > 0 || staffDiscount > 0) && (
                        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                            <label className="block text-sm font-medium text-apple-gray-700 mb-2">已应用优惠</label>
                            <div className="space-y-2">
                                {discounts.map((discount, index) => (
                                    <div key={index} className="flex items-center justify-between text-sm">
                                        <span className="text-apple-gray-700">{discount.description}</span>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-red-600 font-medium">-¥{discount.amount.toFixed(2)}</span>
                                            <button
                                                onClick={() => removeDiscount(discount.discountType)}
                                                className="text-red-500 hover:text-red-700 transition-colors"
                                            >
                                                <i className="fas fa-times text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                ))}
                                {staffDiscount > 0 && (
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-apple-gray-700">员工调整</span>
                                        <div className="flex items-center space-x-2">
                                            <span className="text-red-600 font-medium">-¥{staffDiscount.toFixed(2)}</span>
                                            <button
                                                onClick={() => setStaffDiscount(0)}
                                                className="text-red-500 hover:text-red-700 transition-colors"
                                            >
                                                <i className="fas fa-times text-xs"></i>
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* 备注 */}
                    <div className="mb-4 p-3 bg-apple-gray-50 rounded-lg">
                        <label className="block text-sm font-medium text-apple-gray-700 mb-2">备注</label>
                        <textarea
                            value={orderRemark}
                            onChange={(e) => setOrderRemark(e.target.value)}
                            placeholder="订单备注..."
                            className="w-full px-3 py-2 text-sm border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent resize-none"
                            rows="2"
                        />
                    </div>
                    
                    {/* 购物车列表 */}
                    <div className="space-y-3 mb-6 max-h-64 overflow-y-auto">
                        {cart.length === 0 ? (
                            <div className="text-center py-8">
                                <i className="fas fa-shopping-cart text-4xl text-apple-gray-300 mb-3"></i>
                                <p className="text-apple-gray-500">购物车为空</p>
                            </div>
                        ) : (
                            cart.map((item) => (
                                <div key={item.id} className="flex items-center justify-between p-3 bg-apple-gray-50 rounded-apple">
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-apple-gray-900 truncate">{item.name}</h4>
                                        <p className="text-sm text-apple-gray-500">¥{item.price} × {item.quantity}</p>
                                    </div>
                                    <div className="flex items-center space-x-2 ml-3">
                                        <button 
                                            onClick={() => updateQuantity(item.id, -1)}
                                            className="w-6 h-6 bg-apple-gray-200 rounded-full flex items-center justify-center text-apple-gray-600 hover:bg-apple-gray-300 transition-colors"
                                        >
                                            <i className="fas fa-minus text-xs"></i>
                                        </button>
                                        <span className="w-8 text-center font-medium text-apple-gray-900">{item.quantity}</span>
                                        <button 
                                            onClick={() => updateQuantity(item.id, 1)}
                                            className="w-6 h-6 bg-apple-blue rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors"
                                        >
                                            <i className="fas fa-plus text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>

                    {/* 订单总计 */}
                    {cart.length > 0 && (
                        <>
                            <div className="border-t border-apple-gray-200 pt-4 mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <span className="text-apple-gray-600">小计</span>
                                    <span className="font-medium text-apple-gray-900">¥{calculateTotal().subtotal.toFixed(2)}</span>
                                </div>
                                {calculateTotal().discountAmount > 0 && (
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-apple-gray-600">优惠</span>
                                        <span className="font-medium text-red-600">-¥{calculateTotal().discountAmount.toFixed(2)}</span>
                                    </div>
                                )}
                                <div className="flex justify-between items-center text-lg font-semibold">
                                    <span className="text-apple-gray-900">应付金额</span>
                                    <span className="text-apple-blue font-sf-pro">¥{calculateTotal().total.toFixed(2)}</span>
                                </div>
                            </div>

                            {/* 支付方式选择 */}
                            <div className="p-4 bg-apple-gray-50 rounded-lg">
                                <label className="block text-sm font-medium text-apple-gray-700 mb-3">支付方式</label>
                                <div className="grid grid-cols-2 gap-2">
                                    {Object.values(PAYMENT_METHODS).map((method) => (
                                        <button
                                            key={method.value}
                                            onClick={() => setSelectedPaymentMethod(method.value)}
                                            className={`p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                                                selectedPaymentMethod === method.value
                                                    ? 'border-apple-blue bg-blue-50 text-apple-blue'
                                                    : 'border-apple-gray-200 bg-white text-apple-gray-700 hover:border-apple-gray-300'
                                            }`}
                                        >
                                            <div className="font-medium text-sm">{method.label}</div>
                                            <div className="text-xs text-apple-gray-500 mt-1">{method.description}</div>
                                        </button>
                                    ))}
                                </div>

                                {/* 余额支付提示 */}
                                {selectedPaymentMethod === 'balance' && (
                                    <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div className="text-xs text-blue-600">
                                            {selectedMember ? (
                                                <>
                                                    <i className="fas fa-info-circle mr-1"></i>
                                                    会员余额：¥{(selectedMember.balance || 0).toFixed(2)}
                                                    {selectedMember.balance >= calculateTotal().total ? (
                                                        <span className="text-green-600 ml-2">✓ 余额充足</span>
                                                    ) : (
                                                        <span className="text-red-600 ml-2">✗ 余额不足</span>
                                                    )}
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-exclamation-triangle mr-1"></i>
                                                    请先选择会员
                                                </>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* 创建订单按钮 */}
                            <div className="space-y-3">
                                <button
                                    onClick={handlePayment}
                                    disabled={isProcessing}
                                    className="w-full py-3 bg-apple-blue text-white rounded-xl font-medium hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <i className="fas fa-plus mr-2"></i>
                                    {isProcessing ? '创建中...' : '创建订单'}
                                </button>
                            </div>
                        </>
                    )}
                </div>
                </div>
            ) : (
                /* 订单管理标签页 */
                <div className="space-y-6">
                    {/* 搜索条件 */}
                    <div className="apple-card rounded-2xl p-6">
                        <h3 className="text-lg font-semibold text-apple-gray-900 mb-4">订单查询</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">订单状态</label>
                                <select
                                    value={orderSearchForm.status}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, status: e.target.value})}
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                >
                                    <option value="">全部状态</option>
                                    <option value="PENDING_PAYMENT">待支付</option>
                                    <option value="PROCESSING">制作中</option>
                                    <option value="COMPLETED">已完成</option>
                                    <option value="CANCELLED">已取消</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">支付状态</label>
                                <select
                                    value={orderSearchForm.paymentStatus}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, paymentStatus: e.target.value})}
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                >
                                    <option value="">全部状态</option>
                                    <option value="UNPAID">未支付</option>
                                    <option value="PAID">已支付</option>
                                    <option value="REFUNDED">已退款</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">桌台</label>
                                <select
                                    value={orderSearchForm.tableId}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, tableId: e.target.value})}
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                >
                                    <option value="">全部桌台</option>
                                    {tables.filter(table => table.isActive).map(table => (
                                        <option key={table.id} value={table.id}>
                                            {table.tableNumber}号桌
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">手机号</label>
                                <input
                                    type="text"
                                    value={orderSearchForm.phoneNumber}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, phoneNumber: e.target.value})}
                                    placeholder="请输入手机号"
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                />
                            </div>
                        </div>

                        {/* 时间范围筛选 */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">开始时间</label>
                                <input
                                    type="datetime-local"
                                    value={orderSearchForm.startTime}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, startTime: e.target.value})}
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-apple-gray-700 mb-1">结束时间</label>
                                <input
                                    type="datetime-local"
                                    value={orderSearchForm.endTime}
                                    onChange={(e) => setOrderSearchForm({...orderSearchForm, endTime: e.target.value})}
                                    className="w-full px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent"
                                />
                            </div>
                            <div className="flex items-end space-x-2">
                                <button
                                    onClick={handleOrderSearch}
                                    disabled={ordersLoading}
                                    className="flex-1 py-2 bg-apple-blue text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
                                >
                                    <i className="fas fa-search mr-2"></i>
                                    搜索
                                </button>
                                <button
                                    onClick={resetOrderSearch}
                                    className="px-4 py-2 bg-apple-gray-500 text-white rounded-lg hover:bg-apple-gray-600 transition-colors"
                                >
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* 订单列表 */}
                    <div className="apple-card rounded-2xl p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-apple-gray-900">订单列表</h3>
                            <span className="text-sm text-apple-gray-500">
                                共 {ordersPagination.total} 条记录
                            </span>
                        </div>

                        {ordersLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <i className="fas fa-spinner fa-spin text-2xl text-apple-gray-400"></i>
                                <span className="ml-2 text-apple-gray-600">加载中...</span>
                            </div>
                        ) : orders.length === 0 ? (
                            <div className="text-center py-12">
                                <i className="fas fa-inbox text-4xl text-apple-gray-300 mb-4"></i>
                                <p className="text-apple-gray-500">暂无订单数据</p>
                            </div>
                        ) : (
                            <>
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead>
                                            <tr className="border-b border-apple-gray-200">
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">类型</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">桌台</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">手机号</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">金额</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">状态</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">支付状态</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">支付方式</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">备注</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">创建时间</th>
                                                <th className="text-left py-3 px-4 font-medium text-apple-gray-700">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {orders.map((order) => (
                                                <tr key={order.id} className="border-b border-apple-gray-100 hover:bg-apple-gray-50">
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm">{order.orderTypeDescription}</span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm text-apple-gray-600">
                                                            {order.tableNumber ? `${order.tableNumber}号桌` : '-'}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm text-apple-gray-600">
                                                            {order.phoneNumber || '-'}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <div className="text-sm">
                                                            <div className="text-apple-blue font-medium">¥{order.payableAmount}</div>
                                                            {order.discountAmount > 0 && (
                                                                <div className="text-xs text-apple-gray-500">
                                                                    原价: ¥{order.originalAmount}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                            order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                                            order.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                                                            order.status === 'PENDING_PAYMENT' ? 'bg-yellow-100 text-yellow-800' :
                                                            'bg-blue-100 text-blue-800'
                                                        }`}>
                                                            {order.statusDescription}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                            order.paymentStatus === 'PAID' ? 'bg-green-100 text-green-800' :
                                                            order.paymentStatus === 'REFUNDED' ? 'bg-red-100 text-red-800' :
                                                            'bg-gray-100 text-gray-800'
                                                        }`}>
                                                            {order.paymentStatusDescription}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                            order.paymentMethod === 'cash' ? 'bg-green-100 text-green-800' :
                                                            order.paymentMethod === 'balance' ? 'bg-blue-100 text-blue-800' :
                                                            order.paymentMethod === 'wechat_pay' ? 'bg-green-100 text-green-800' :
                                                            order.paymentMethod === 'offline_scan' ? 'bg-purple-100 text-purple-800' :
                                                            'bg-gray-100 text-gray-800'
                                                        }`}>
                                                            {getPaymentMethodDisplay(order.paymentMethod)}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <div className="text-sm text-apple-gray-600 max-w-32 truncate" title={order.remark || ''}>
                                                            {order.remark || '-'}
                                                        </div>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm text-apple-gray-600">
                                                            {new Date(order.createdAt).toLocaleString()}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <button
                                                            onClick={() => handleViewOrder(order.id)}
                                                            className="text-apple-blue hover:text-blue-600 transition-colors"
                                                        >
                                                            <i className="fas fa-eye mr-1"></i>
                                                            查看
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>

                                {/* 分页 */}
                                {ordersPagination.pages > 1 && (
                                    <div className="flex items-center justify-between mt-6">
                                        <div className="text-sm text-apple-gray-600">
                                            第 {ordersPagination.current} 页，共 {ordersPagination.pages} 页
                                        </div>
                                        <div className="flex space-x-2">
                                            <button
                                                onClick={() => fetchOrders(ordersPagination.current - 1)}
                                                disabled={ordersPagination.current <= 1}
                                                className="px-3 py-1 border border-apple-gray-300 rounded text-sm hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                上一页
                                            </button>
                                            <button
                                                onClick={() => fetchOrders(ordersPagination.current + 1)}
                                                disabled={ordersPagination.current >= ordersPagination.pages}
                                                className="px-3 py-1 border border-apple-gray-300 rounded text-sm hover:bg-apple-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                下一页
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            )}

            {/* 模态框 */}
            <MemberSearchModal
                visible={memberSearchModal}
                onClose={() => setMemberSearchModal(false)}
                onSelectMember={handleSelectMember}
            />

            <OrderModal
                visible={orderModal.visible}
                orderId={orderModal.orderId}
                onClose={() => setOrderModal({ visible: false, orderId: null })}
                onOrderUpdate={handleOrderUpdate}
            />
        </Layout>
    );
};

export default POS;
