// 茶馆收银系统 - 配置文件

// API 配置
export const API_CONFIG = {
    // 开发环境配置
    development: {
        baseURL: 'http://127.0.0.1:8081', // 开发环境 API 地址
        timeout: 10000, // 请求超时时间（毫秒）
    },
    
    // 生产环境配置
    production: {
        baseURL: 'https://your-api-domain.com', // 生产环境 API 地址
        timeout: 15000,
    },
    
    // 测试环境配置
    testing: {
        baseURL: 'http://localhost:3000', // 测试环境 API 地址
        timeout: 10000,
    }
};

// 当前环境（可以根据需要修改）
export const CURRENT_ENV = process.env.NODE_ENV === 'production' ? 'production' : 'development';

// 获取当前环境的配置
export const getCurrentConfig = () => {
    return API_CONFIG[CURRENT_ENV];
};

// 应用配置
export const APP_CONFIG = {
    name: '茶馆收银系统',
    version: '1.0.0',
    
    // 登录配置
    auth: {
        tokenKey: 'accessToken',
        refreshTokenKey: 'refreshToken',
        userInfoKeys: ['staffId', 'username', 'fullName', 'storeId', 'roleId'],
        rememberLoginKey: 'rememberLogin',
        loginPath: '/login',
        dashboardPath: '/dashboard',
    },
    
    // UI 配置
    ui: {
        theme: 'tea-house', // 茶馆主题
        primaryColor: '#007AFF',
        secondaryColor: '#34C759',
        mobileBreakpoint: 768,
    },
    
    // 功能开关
    features: {
        rememberLogin: true,
        autoLogout: true,
        passwordToggle: true,
        loadingAnimation: true,
    }
};

// 路由配置
export const ROUTES = {
    LOGIN: '/login',
    DASHBOARD: '/dashboard',
    POS: '/pos',
    INVENTORY: '/inventory',
    MEMBERS: '/members',
    TABLES: '/tables',
    REPORTS: '/reports',
    SETTINGS: '/settings',
};

// 本地存储键名
export const STORAGE_KEYS = {
    ACCESS_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    USER_INFO: 'userInfo',
    REMEMBER_LOGIN: 'rememberLogin',
    THEME: 'theme',
};

console.log(`🍃 ${APP_CONFIG.name} v${APP_CONFIG.version} 已加载`);
console.log(`📡 当前环境: ${CURRENT_ENV}`);
console.log(`🔗 API 基础地址: ${getCurrentConfig().baseURL}`);
