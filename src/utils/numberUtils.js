/**
 * 数字处理工具函数
 */

/**
 * 安全地将大整数转换为字符串
 * 避免 JavaScript 数字精度丢失问题
 * @param {any} value - 要转换的值
 * @returns {string} - 转换后的字符串
 */
export const safeStringify = (value) => {
    if (value === null || value === undefined) {
        return '';
    }
    
    // 如果已经是字符串，直接返回
    if (typeof value === 'string') {
        return value;
    }
    
    // 如果是数字，转换为字符串
    if (typeof value === 'number') {
        // 对于大整数，使用 toString() 可能会有精度问题
        // 但这里我们假设后端会以字符串形式发送大整数
        return value.toString();
    }
    
    // 其他类型转换为字符串
    return String(value);
};

/**
 * 安全地处理会员ID
 * @param {any} memberId - 会员ID
 * @returns {string} - 处理后的会员ID字符串
 */
export const safeMemberId = (memberId) => {
    const result = safeStringify(memberId);
    
    // 验证是否为有效的会员ID（应该是数字字符串）
    if (result && !/^\d+$/.test(result)) {
        console.warn('Invalid memberId format:', memberId);
    }
    
    return result;
};

/**
 * 格式化显示会员ID
 * @param {any} memberId - 会员ID
 * @returns {string} - 格式化后的显示字符串
 */
export const formatMemberId = (memberId) => {
    const id = safeMemberId(memberId);
    return id || '未设置';
};

/**
 * 安全地处理数字数组或对象中的大整数字段
 * @param {any} data - 要处理的数据
 * @param {string[]} fields - 需要转换为字符串的字段名数组
 * @returns {any} - 处理后的数据
 */
export const safeProcessLargeIntegers = (data, fields = ['memberId', 'id']) => {
    if (!data) return data;
    
    if (Array.isArray(data)) {
        return data.map(item => safeProcessLargeIntegers(item, fields));
    }
    
    if (typeof data === 'object') {
        const processed = { ...data };
        fields.forEach(field => {
            if (processed[field] !== undefined) {
                processed[field] = safeStringify(processed[field]);
            }
        });
        return processed;
    }
    
    return data;
};
