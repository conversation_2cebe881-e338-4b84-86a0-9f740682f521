import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/api';
import { STORAGE_KEYS } from '../utils/config';

// 初始状态
const initialState = {
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null,
};

// Action 类型
const AUTH_ACTIONS = {
    LOGIN_START: 'LOGIN_START',
    LOGIN_SUCCESS: 'LOGIN_SUCCESS',
    LOGIN_FAILURE: 'LOGIN_FAILURE',
    LOGOUT: 'LOGOUT',
    SET_LOADING: 'SET_LOADING',
    SET_USER: 'SET_USER',
    CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer
const authReducer = (state, action) => {
    switch (action.type) {
        case AUTH_ACTIONS.LOGIN_START:
            return {
                ...state,
                loading: true,
                error: null,
            };
        case AUTH_ACTIONS.LOGIN_SUCCESS:
            return {
                ...state,
                isAuthenticated: true,
                user: action.payload.user,
                loading: false,
                error: null,
            };
        case AUTH_ACTIONS.LOGIN_FAILURE:
            return {
                ...state,
                isAuthenticated: false,
                user: null,
                loading: false,
                error: action.payload.error,
            };
        case AUTH_ACTIONS.LOGOUT:
            return {
                ...state,
                isAuthenticated: false,
                user: null,
                loading: false,
                error: null,
            };
        case AUTH_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: action.payload,
            };
        case AUTH_ACTIONS.SET_USER:
            return {
                ...state,
                user: action.payload,
                isAuthenticated: true,
                loading: false,
            };
        case AUTH_ACTIONS.CLEAR_ERROR:
            return {
                ...state,
                error: null,
            };
        default:
            return state;
    }
};

// 创建上下文
const AuthContext = createContext();

// AuthProvider 组件
export const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);

    // 检查本地存储的认证信息
    useEffect(() => {
        const checkAuth = async () => {
            const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
            const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

            if (token && userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
                } catch (error) {
                    console.error('解析用户信息失败:', error);
                    logout();
                }
            } else {
                dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            }
        };

        checkAuth();
    }, []);

    // 登录
    const login = async (credentials, rememberMe = false) => {
        dispatch({ type: AUTH_ACTIONS.LOGIN_START });

        try {
            const response = await authAPI.login(credentials);
            const { data } = response.data;

            if (response.data.code === 200) {
                // 存储认证信息
                localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, data.accessToken);
                localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, data.refreshToken);
                
                // 构建用户信息
                const userInfo = {
                    staffId: data.staffId,
                    username: data.username,
                    fullName: data.fullName,
                    storeId: data.storeId,
                    roleId: data.roleId,
                };
                
                localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
                
                // 记住登录状态
                if (rememberMe) {
                    localStorage.setItem(STORAGE_KEYS.REMEMBER_LOGIN, 'true');
                }

                dispatch({
                    type: AUTH_ACTIONS.LOGIN_SUCCESS,
                    payload: { user: userInfo },
                });

                return { success: true, data: userInfo };
            } else {
                throw new Error(response.data.msg || '登录失败');
            }
        } catch (error) {
            const errorMessage = error.response?.data?.msg || error.message || '登录失败';
            dispatch({
                type: AUTH_ACTIONS.LOGIN_FAILURE,
                payload: { error: errorMessage },
            });
            return { success: false, error: errorMessage };
        }
    };

    // 登出
    const logout = async () => {
        try {
            await authAPI.logout();
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 清除本地存储
            localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.USER_INFO);
            localStorage.removeItem(STORAGE_KEYS.REMEMBER_LOGIN);

            dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
    };

    // 刷新 token
    const refreshToken = async () => {
        const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        
        if (!refreshTokenValue) {
            logout();
            return false;
        }

        try {
            const response = await authAPI.refreshToken(refreshTokenValue);
            const { data } = response.data;

            if (response.data.code === 200) {
                localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, data.accessToken);
                localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, data.refreshToken);
                return true;
            } else {
                logout();
                return false;
            }
        } catch (error) {
            console.error('刷新 token 失败:', error);
            logout();
            return false;
        }
    };

    // 清除错误
    const clearError = () => {
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
    };

    // 更新用户信息
    const updateUser = (userInfo) => {
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: userInfo });
    };

    const value = {
        ...state,
        login,
        logout,
        refreshToken,
        clearError,
        updateUser,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

// 自定义 Hook
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export default AuthContext;
