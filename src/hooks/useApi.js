import { useState, useEffect } from 'react';

// 通用的 API 请求 Hook
export const useApi = (apiFunction, dependencies = []) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await apiFunction();
                if (response.data.code === 200) {
                    setData(response.data.data);
                } else {
                    setError(response.data.msg || '请求失败');
                }
            } catch (err) {
                setError(err.response?.data?.msg || err.message || '网络错误');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, dependencies);

    const refetch = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await apiFunction();
            if (response.data.code === 200) {
                setData(response.data.data);
            } else {
                setError(response.data.msg || '请求失败');
            }
        } catch (err) {
            setError(err.response?.data?.msg || err.message || '网络错误');
        } finally {
            setLoading(false);
        }
    };

    return { data, loading, error, refetch };
};

// 分页数据 Hook
export const usePaginatedApi = (apiFunction, initialParams = {}) => {
    const [data, setData] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [params, setParams] = useState(initialParams);

    const fetchData = async (page = 1, size = 10, searchParams = {}) => {
        try {
            setLoading(true);
            setError(null);
            
            const requestParams = {
                page,
                pageSize: size,
                ...params,
                ...searchParams,
            };

            const response = await apiFunction(requestParams);
            
            if (response.data.code === 200) {
                const { list, total, current, pageSize } = response.data.data;
                setData(list || []);
                setPagination({
                    current: current || page,
                    pageSize: pageSize || size,
                    total: total || 0,
                });
            } else {
                setError(response.data.msg || '请求失败');
            }
        } catch (err) {
            setError(err.response?.data?.msg || err.message || '网络错误');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData(pagination.current, pagination.pageSize, params);
    }, []);

    const changePage = (page, pageSize) => {
        fetchData(page, pageSize, params);
    };

    const search = (searchParams) => {
        setParams({ ...params, ...searchParams });
        fetchData(1, pagination.pageSize, { ...params, ...searchParams });
    };

    const refresh = () => {
        fetchData(pagination.current, pagination.pageSize, params);
    };

    return {
        data,
        pagination,
        loading,
        error,
        changePage,
        search,
        refresh,
    };
};

// 表单提交 Hook
export const useSubmit = (submitFunction) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);

    const submit = async (data) => {
        try {
            setLoading(true);
            setError(null);
            setSuccess(false);

            const response = await submitFunction(data);
            
            if (response.data.code === 200) {
                setSuccess(true);
                return { success: true, data: response.data.data };
            } else {
                const errorMsg = response.data.msg || '提交失败';
                setError(errorMsg);
                return { success: false, error: errorMsg };
            }
        } catch (err) {
            const errorMsg = err.response?.data?.msg || err.message || '网络错误';
            setError(errorMsg);
            return { success: false, error: errorMsg };
        } finally {
            setLoading(false);
        }
    };

    const reset = () => {
        setLoading(false);
        setError(null);
        setSuccess(false);
    };

    return { submit, loading, error, success, reset };
};

// 本地存储 Hook
export const useLocalStorage = (key, initialValue) => {
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error(`Error reading localStorage key "${key}":`, error);
            return initialValue;
        }
    });

    const setValue = (value) => {
        try {
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            setStoredValue(valueToStore);
            window.localStorage.setItem(key, JSON.stringify(valueToStore));
        } catch (error) {
            console.error(`Error setting localStorage key "${key}":`, error);
        }
    };

    const removeValue = () => {
        try {
            setStoredValue(initialValue);
            window.localStorage.removeItem(key);
        } catch (error) {
            console.error(`Error removing localStorage key "${key}":`, error);
        }
    };

    return [storedValue, setValue, removeValue];
};

// 防抖 Hook
export const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};
