import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import POS from './pages/POS';
import Members from './pages/Members';
import Inventory from './pages/Inventory';
import Tables from './pages/Tables';
import { ROUTES } from './utils/config';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App min-h-screen bg-apple-gray-50">
          <Routes>
            {/* 公开路由 */}
            <Route path={ROUTES.LOGIN} element={<Login />} />

            {/* 受保护的路由 */}
            <Route path={ROUTES.DASHBOARD} element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            <Route path={ROUTES.POS} element={
              <ProtectedRoute>
                <POS />
              </ProtectedRoute>
            } />

            <Route path={ROUTES.INVENTORY} element={
              <ProtectedRoute>
                <Inventory />
              </ProtectedRoute>
            } />

            <Route path={ROUTES.MEMBERS} element={
              <ProtectedRoute>
                <Members />
              </ProtectedRoute>
            } />

            <Route path={ROUTES.TABLES} element={
              <ProtectedRoute>
                <Tables />
              </ProtectedRoute>
            } />

            <Route path={ROUTES.REPORTS} element={
              <ProtectedRoute>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <i className="fas fa-chart-bar text-6xl text-apple-gray-300 mb-4"></i>
                    <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2">数据报表</h2>
                    <p className="text-apple-gray-600">功能开发中...</p>
                  </div>
                </div>
              </ProtectedRoute>
            } />

            <Route path={ROUTES.SETTINGS} element={
              <ProtectedRoute>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <i className="fas fa-cog text-6xl text-apple-gray-300 mb-4"></i>
                    <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2">系统设置</h2>
                    <p className="text-apple-gray-600">功能开发中...</p>
                  </div>
                </div>
              </ProtectedRoute>
            } />



            {/* 默认重定向 */}
            <Route path="/" element={<Navigate to={ROUTES.DASHBOARD} replace />} />

            {/* 404 页面 */}
            <Route path="*" element={
              <div className="min-h-screen flex items-center justify-center bg-apple-gray-50">
                <div className="text-center">
                  <i className="fas fa-exclamation-triangle text-6xl text-apple-gray-300 mb-4"></i>
                  <h2 className="text-2xl font-semibold text-apple-gray-900 mb-2">页面未找到</h2>
                  <p className="text-apple-gray-600 mb-4">您访问的页面不存在</p>
                  <a href={ROUTES.DASHBOARD} className="apple-button px-6 py-2 rounded-apple">
                    返回主页
                  </a>
                </div>
              </div>
            } />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
