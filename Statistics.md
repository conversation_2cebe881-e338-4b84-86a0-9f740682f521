1、获取今日业务统计数据
GET /api/v1/statistics/today
{
"code": 200,
"msg": "今日统计数据",
"data": {
"todaySalesAmount": 12569.00,
"salesGrowthPercentage": 100.00,
"todayOrderCount": 2,
"orderGrowthPercentage": 100.00,
"todayNewMemberCount": 0,
"memberGrowthPercentage": 0
},
"timestamp": 1753510759152
}
2、获取最近5笔订单详情
GET /api/v1/statistics/recent-orders
{"code":200,"msg":"最近5笔订单","data":[{"orderNo":"20250726125025809428","totalAmount":12312.00,"items":[{"productName":"123123","quantity":1}]},{"orderNo":"20250726125003335492","totalAmount":257.00,"items":[{"productName":"234324ed","quantity":1},{"productName":"432432","quantity":1}]},{"orderNo":"20250724224632499616","totalAmount":12312.00,"items":[{"productName":"123123","quantity":1}]},{"orderNo":"20250724224248226252","totalAmount":134.00,"items":[{"productName":"432432","quantity":1}]},{"orderNo":"20250724222040855035","totalAmount":370.00,"items":[{"productName":"正山小种222","quantity":1},{"productName":"正山小种修改1","quantity":1}]}],"timestamp":1753510825548}

3、创建备忘录
POST /api/v1/memos/create
request:
{
"content": "备忘录11"
}
response:
{"code":200,"msg":"备忘录创建成功","timestamp":1753511987788}
4、分页查询备忘录
POST /api/v1/memos/query
request:
{
    "size": 10,
    "current": 1
}
response:
{"code":200,"msg":"备忘录列表","data":{"current":1,"size":10,"total":1,"pages":1,"records":[{"id":1,"content":"备忘录11","createdAt":"2025-07-26T12:53:07.788001","updatedAt":"2025-07-26T12:53:0
5、修改备忘录状态
POST /api/v1/memos/update-status
request:
{
"ids": [
1
],
"status": "DONE"
}
response:
{"code":200,"msg":"备忘录状态修改成功","timestamp":1753512067000}
