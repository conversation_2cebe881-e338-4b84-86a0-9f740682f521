1、创建分类
POST /api/v1/product-categories/create
request:
{
"name": "分类2",
"parentId": 0,
"sortOrder": 1
}
response:
{
"code": 200,
"msg": "分类创建成功",
"timestamp": 1753189257179
}
2、修改分类
POST /api/v1/product-categories/update
request:
{
"id": 2,
"name": "分类2",
"parentId": 0,
"sortOrder": 1
}
response:
{
"code": 200,
"msg": "分类修改成功",
"timestamp": 1753189257179
}
3、删除分类
POST /api/v1/product-categories/delete/{id}
response:
{
"code": 200,
"msg": "分类删除成功",
"data": null,
"timestamp": 1753190230633
}
4、查询顶级分类
GET /api/v1/product-categories/top-level
response:
{
"code": 200,
"msg": "操作成功",
"data": [
{
"id": 2,
"name": "分类2",
"parentId": 0,
"storeId": 1,
"sortOrder": 1,
"createdAt": "2025-07-22T21:00:57.173227",
"updatedAt": "2025-07-22T21:00:57.173227",
"children": null,
"productCount": null
}
],
"timestamp": 1753190299509
}
5、创建商品
POST /api/v1/products/create   
request:
{
"name": "正山小种",
"categoryId": 2,
"price": 120,
"stockQuantity": 200,
"alertQuantity": 20,
"status": "ARCHIVED",
"productCode": "123",
"description": "描述",
"imageUrl": "http://dummyimage.com/400x400"
}
response:
{
"code": 200,
"msg": "商品创建成功",
"timestamp": 1753190396754
}
6、修改商品
POST /api/v1/products/update
request:
{
"id": 1,
"name": "正山小种修改1",
"categoryId": 2,
"price": 300,
"stockQuantity": 200,
"alertQuantity": 20,
"imageUrl": "http://dummyimage.com/400x400",
"status": "ARCHIVED",
"productCode": "11",
"description": "描述123"
}
response:
{
"code": 200,
"msg": "商品修改成功",
"timestamp": 1753190396754
}
7、删除商品
POST /api/v1/products/delete/{id}
response:
{
"code": 200,
"msg": "商品删除成功",
"timestamp": 1753190230633
}
8、根据分类查询商品
GET /api/v1/products/category/{categoryId}
response:
{
"code": 200,
"msg": "操作成功",
"data": [
{
"id": 2,
"name": "正山小种222",
"description": "描述",
"imageUrl": "http://dummyimage.com/400x400",
"categoryId": 2,
"categoryName": null,
"price": 120.00,
"stockQuantity": 200,
"alertQuantity": 20,
"productCode": "123",
"status": "ARCHIVED",
"statusDescription": "已下架",
"storeId": 1,
"createdAt": "2025-07-22T21:23:23.59294",
"updatedAt": "2025-07-22T21:23:23.59294",
"isLowStock": false
}
],
"timestamp": 1753190633601
}
9、上架商品
POST /api/v1/products/publish/{id}
response:
{
"code": 200,
"msg": "商品上架成功",
"timestamp": 1753190719000
}
10、下架商品
POST /api/v1/products/archive/{id}
response:
{
"code": 200,
"msg": "商品下架成功",
"timestamp": 1753190719000
}
11、标记为售罄
POST /api/v1/products/sold-out/{id}
response:
{
"code": 200,
"msg": "商品标记为售罄成功",
"timestamp": 1753190719000
}
12、取消售罄
POST /api/v1/products/sold-out/{id}
response:
{
"code": 200,
"msg": "商品取消售罄成功",
"timestamp": 1753190719000
}
