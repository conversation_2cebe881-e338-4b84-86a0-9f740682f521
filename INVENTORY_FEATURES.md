# 商品、库存管理模块功能说明

## 📦 已完成功能

### 1. 商品分类管理
- ✅ **分类列表展示**：以表格形式展示所有商品分类
- ✅ **新增分类**：支持创建新的商品分类
- ✅ **编辑分类**：修改分类名称和排序值
- ✅ **删除分类**：删除不需要的分类（会提示影响的商品）
- ✅ **排序功能**：支持设置分类的显示顺序

### 2. 商品管理
- ✅ **商品列表展示**：支持网格视图和表格视图切换
- ✅ **新增商品**：完整的商品信息录入表单
- ✅ **编辑商品**：修改商品的所有信息
- ✅ **删除商品**：删除不需要的商品
- ✅ **商品搜索**：按商品名称或编码搜索
- ✅ **分类筛选**：按商品分类筛选商品列表
- ✅ **库存预警**：低库存商品的视觉提醒

### 3. 商品状态管理
- ✅ **上架商品**：将下架的商品重新上架
- ✅ **下架商品**：将上架的商品下架
- ✅ **标记售罄**：标记商品为售罄状态
- ✅ **取消售罄**：取消商品的售罄状态

### 4. 分页功能
- ✅ **服务端分页**：使用 `POST /api/v1/products/query` 接口实现
- ✅ **分页控件**：首页、上一页、下一页、末页导航
- ✅ **页面大小选择**：支持 10/50 条每页
- ✅ **分页信息显示**：当前页数、总页数、总记录数
- ✅ **智能分页**：搜索和筛选时自动重置到第一页
- ✅ **快速跳转**：支持直接跳转到指定页面

### 5. 搜索优化
- ✅ **防抖搜索**：500ms 防抖，避免频繁 API 调用
- ✅ **实时筛选**：按商品名称、编码、分类筛选
- ✅ **备用机制**：分页接口失败时自动降级到普通接口

### 6. 用户界面特性
- ✅ **Apple 风格设计**：遵循项目的 Apple 设计规范
- ✅ **响应式布局**：适配桌面端和移动端
- ✅ **交互反馈**：完整的加载状态和错误处理
- ✅ **数据验证**：表单输入的完整验证
- ✅ **状态指示**：清晰的商品状态显示

## 🔧 技术实现

### API 接口对接
根据 `product.md` 文档和新增的分页查询需求，完整对接了以下 API：

**商品分类 API**：
- `GET /api/v1/product-categories/top-level` - 获取顶级分类
- `POST /api/v1/product-categories/create` - 创建分类
- `POST /api/v1/product-categories/update` - 更新分类
- `POST /api/v1/product-categories/delete/{id}` - 删除分类

**商品管理 API**：
- `POST /api/v1/products/query` - **分页查询商品** (支持 name, categoryId 参数)
- `GET /api/v1/products/category/{categoryId}` - 根据分类查询商品
- `POST /api/v1/products/create` - 创建商品
- `POST /api/v1/products/update` - 更新商品
- `POST /api/v1/products/delete/{id}` - 删除商品

**商品状态 API**：
- `POST /api/v1/products/publish/{id}` - 上架商品
- `POST /api/v1/products/archive/{id}` - 下架商品
- `POST /api/v1/products/sold-out/{id}` - 标记/取消售罄 (同一接口)

### 组件架构
```
src/pages/Inventory.js          # 主页面组件
├── src/components/ProductModal.js    # 商品新增/编辑弹窗
├── src/components/CategoryModal.js   # 分类新增/编辑弹窗
└── src/components/ProductCard.js     # 商品卡片组件
```

### 状态管理
- 使用 React Hooks (useState, useEffect) 管理组件状态
- 实现了完整的数据流：获取 → 展示 → 操作 → 更新
- 支持实时数据刷新和状态同步

## 🎨 界面设计

### 标签页导航
- **商品管理**：商品的增删改查和状态管理
- **分类管理**：商品分类的维护

### 视图模式
- **网格视图**：卡片式展示，适合浏览和快速操作
- **表格视图**：列表式展示，适合批量管理

### 交互特性
- **搜索筛选**：实时搜索和分类筛选
- **状态切换**：一键切换商品状态
- **批量操作**：支持批量选择和操作（表格视图）
- **响应式设计**：自适应不同屏幕尺寸

## 📱 使用说明

### 访问路径
在系统中点击侧边栏的"库存管理"或直接访问 `/inventory` 路径

### 商品管理操作
1. **新增商品**：点击"新增商品"按钮，填写商品信息
2. **编辑商品**：点击商品卡片或表格行的编辑按钮
3. **删除商品**：点击删除按钮并确认
4. **状态管理**：使用商品卡片上的状态按钮快速切换
5. **搜索筛选**：使用顶部的搜索框和分类筛选器

### 分类管理操作
1. **新增分类**：在分类管理标签页点击"新增分类"
2. **编辑分类**：点击分类表格中的编辑按钮
3. **删除分类**：点击删除按钮（会提示影响的商品数量）
4. **排序设置**：通过排序值控制分类显示顺序



## 🔄 数据流程

1. **页面加载** → 获取商品列表和分类列表
2. **用户操作** → 调用相应的 API 接口
3. **操作成功** → 刷新数据并更新界面
4. **错误处理** → 显示错误信息并保持当前状态

## 🎯 核心特性

- **完整的 CRUD 操作**：支持商品和分类的完整生命周期管理
- **实时状态管理**：商品状态的实时切换和同步
- **智能库存预警**：低库存商品的自动识别和提醒
- **灵活的视图模式**：适应不同使用场景的界面切换
- **完善的数据验证**：确保数据的完整性和有效性
- **优雅的错误处理**：用户友好的错误提示和恢复机制

## 🚀 后续扩展

该模块为后续功能扩展预留了接口：
- 批量导入/导出功能
- 商品图片上传功能
- 库存盘点功能
- 进货管理功能
- 更多的筛选和排序选项

---

该模块完全按照 `product.md` 中定义的 API 规范实现，确保与后端服务的完美对接。
